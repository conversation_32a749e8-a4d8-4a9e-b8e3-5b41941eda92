"use client";
import React, { useState } from "react";
import { Edit, Plus } from "lucide-react";
import TableFilter from "./TableFilter";

const tables = [
  {
    id: 1,
    status: "active",
    setCapacity: 4,
    tableCode: "IN01",
    tableAvailability: "running",
    kotCount: 1,
    area: "INDOOR",
  },
  {
    id: 2,
    status: "inactive",
    setCapacity: 4,
    tableCode: "IN02",
    tableAvailability: "available",
    area: "INDOOR",
  },
  {
    id: 3,
    status: "active",
    setCapacity: 4,
    tableCode: "IN03",
    tableAvailability: "reserved",
    area: "INDOOR",
  },
  {
    id: 4,
    status: "active",
    setCapacity: 4,
    tableCode: "IN04",
    tableAvailability: "available",
    area: "INDOOR",
  },
  {
    id: 5,
    status: "active",
    setCapacity: 4,
    tableCode: "IN05",
    tableAvailability: "available",
    area: "INDOOR",
  },
  {
    id: 6,
    status: "active",
    setCapacity: 6,
    tableCode: "IN06",
    tableAvailability: "available",
    area: "INDOOR",
  },
  {
    id: 7,
    status: "active",
    setCapacity: 6,
    tableCode: "IN07",
    tableAvailability: "running",
    kotCount: 2,
    area: "INDOOR",
  },
  {
    id: 8,
    status: "active",
    setCapacity: 2,
    tableCode: "IN08",
    tableAvailability: "available",
    area: "INDOOR",
  },
  {
    id: 9,
    status: "active",
    setCapacity: 4,
    tableCode: "TR01",
    tableAvailability: "available",
    area: "TERRACE",
  },
  {
    id: 10,
    status: "active",
    setCapacity: 6,
    tableCode: "TR02",
    tableAvailability: "running",
    kotCount: 1,
    area: "TERRACE",
  },
  {
    id: 11,
    status: "active",
    setCapacity: 4,
    tableCode: "TR03",
    tableAvailability: "reserved",
    area: "TERRACE",
  },
  {
    id: 12,
    status: "active",
    setCapacity: 8,
    tableCode: "TR04",
    tableAvailability: "available",
    area: "TERRACE",
  },
];

// Filter Component
// const TableFilter = ({ selectedArea, onAreaChange }) => {
//   const areas = ["All Areas", "INDOOR", "TERRACE"];

//   return (
//     <div className="mb-6">
//       <div className="flex gap-2 justify-between">
//         <div className="flex gap-2">
//         {areas.map((area) => (
//           <button
//             key={area}
//             onClick={() => onAreaChange(area)}
//             className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
//               selectedArea === area
//                 ? "text-white shadow-sm"
//                 : "text-gray-600 bg-white hover:bg-gray-50 border border-gray-200"
//             }`}
//             style={selectedArea === area ? { backgroundColor: "#FF6500" } : {}}
//           >
//             {area}
//           </button>
//         ))}
//         </div>
//         <button
//           onClick={() => alert("Add new modifier")}
//           className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
//         >
//           <svg
//             className="w-5 h-5"
//             fill="none"
//             stroke="currentColor"
//             strokeWidth="2"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M12 4v16m8-8H4"
//             />
//           </svg>
//           إضافة طاوله جديده
//         </button>
//       </div>

//       {/* Area Info */}
//       <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
//         <span className="font-semibold text-gray-800">
//           {selectedArea === "All Areas" ? "ALL AREAS" : selectedArea}
//         </span>
//         <span>
//           {selectedArea === "All Areas"
//             ? `${tables.length} Table${tables.length !== 1 ? "s" : ""}`
//             : `${tables.filter((t) => t.area === selectedArea).length} Table${
//                 tables.filter((t) => t.area === selectedArea).length !== 1
//                   ? "s"
//                   : ""
//               }`}
//         </span>
//       </div>
//     </div>
//   );
// };

function TableGrid() {
  const [selectedArea, setSelectedArea] = useState("All Areas");

  const filteredTables =
    selectedArea === "All Areas"
      ? tables
      : tables.filter((t) => t.area === selectedArea);

  const getTableBgColor = (availability) => {
    switch (availability) {
      case "running":
        return "bg-blue-100 border-blue-300";
      case "reserved":
        return "bg-green-100 border-green-300";
      case "available":
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const getTableCodeBg = (availability) => {
    switch (availability) {
      case "running":
        return "bg-blue-200 text-blue-800";
      case "reserved":
        return "bg-green-200 text-green-800";
      case "available":
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Filter Component */}
        <TableFilter
          tables={tables}
          selectedArea={selectedArea}
          onAreaChange={setSelectedArea}
        />

        {/* Table Grid */}
        <div className="grid grid-cols-4 gap-4">
          {filteredTables.map((table) => (
            <div
              key={table.id}
              className={`${getTableBgColor(
                table.tableAvailability
              )} border-2 rounded-lg p-4 relative shadow-sm hover:shadow-md transition-shadow`}
            >
              {/* Table Code Header */}
              <div
                className={`${getTableCodeBg(
                  table.tableAvailability
                )} rounded px-2 py-1 text-sm font-semibold mb-3 inline-block`}
              >
                {table.tableCode}
              </div>

              {/* Capacity */}
              <div className="text-right text-sm text-gray-600 mb-2">
                {table.setCapacity} Seat(s)
              </div>

              {/* KOT Count (if running) */}
              {table.tableAvailability === "running" && table.kotCount && (
                <div className="text-right text-sm font-semibold text-gray-800 mb-3">
                  {table.kotCount} KOT
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between items-center mt-4">
                {table.tableAvailability === "running" ? (
                  <>
                    <button className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                      Show Order
                    </button>
                    <button
                      className="px-3 py-1 text-xs text-white rounded hover:opacity-90 transition-opacity flex items-center gap-1"
                      style={{ backgroundColor: "#FF6500" }}
                    >
                      <Plus size={12} />
                      New KOT
                    </button>
                  </>
                ) : (
                  <button className="ml-auto p-1 hover:bg-gray-200 rounded transition-colors">
                    <Edit size={16} className="text-gray-600" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default TableGrid;
