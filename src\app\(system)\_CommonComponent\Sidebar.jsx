// // // // // // "use client";

// // // // // // import { useEffect, useState } from "react";
// // // // // // import Link from "next/link";
// // // // // // import Image from "next/image";
// // // // // // import { usePathname } from "next/navigation";
// // // // // // import {
// // // // // //   LayoutDashboard,
// // // // // //   BarChart2,
// // // // // //   Home,
// // // // // //   AlertTriangle,
// // // // // //   Users2,
// // // // // //   Calendar,
// // // // // //   <PERSON><PERSON><PERSON><PERSON><PERSON>ban,
// // // // // //   Settings,
// // // // // //   X,
// // // // // //   Menu,
// // // // // // } from "lucide-react";

// // // // // // import { useSidebar } from "@/context/SidebarContext";
// // // // // // import { useI18n } from "@/context/translate-api";

// // // // // // const Sidebar = () => {
// // // // // //   const { isOpen, toggleSidebar } = useSidebar();
// // // // // //   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
// // // // // //   const pathname = usePathname();
// // // // // //   const { t } = useI18n();
// // // // // //   const [settingsOpen, setSettingsOpen] = useState(false);
// // // // // //   const [ordersOpen, setOrdersOpen] = useState(false);
// // // // // //   const [branches] = useState([
// // // // // //     { id: 1, name: "DEGLA" },
// // // // // //     { id: 2, name: "MAADI" },
// // // // // //     // Add more branches as needed
// // // // // //   ]);
// // // // // //   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
// // // // // //   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

// // // // // //   const cancelLogout = () => setShowLogoutConfirm(false);

// // // // // //   const isLinkActive = (path) => {
// // // // // //     if (path === "/dashboard" && pathname === "/dashboard") return true;
// // // // // //     return pathname.startsWith(path) && path !== "/dashboard";
// // // // // //   };

// // // // // //   // Dummy data for sidebar menu
// // // // // //   const sidebarMenu = [
// // // // // //     { href: "/dashboard", icon: Calendar, label: t.Ai },
// // // // // //     { href: "/itemMenue", icon: LayoutDashboard, label: "itemMenue" },
// // // // // //     { href: "/modefiers", icon: BarChart2, label: "إضافات" },
// // // // // //     { href: "/itemModefiers", icon: Home, label: "عناصر الإضافات" },
// // // // // //     { href: "/areas", icon: Users2, label: "الطوابق" },
// // // // // //     { href: "/tabels", icon: FolderKanban, label: "الطاولات" },
// // // // // //     { href: "/qrcodes", icon: FolderKanban, label: "Qr" },
// // // // // //     { href: "/staff", icon: FolderKanban, label: "الطاقم" },
// // // // // //     { href: "/customers", icon: FolderKanban, label: "الزبائن" },
// // // // // //     { href: "/pos", icon: FolderKanban, label: "pos" },
// // // // // //     { href: "/Settings", icon: FolderKanban, label: "Settings" },
// // // // // //     {
// // // // // //       href: "/diliveryExcutive",
// // // // // //       icon: FolderKanban,
// // // // // //       label: "diliveryExcutive",
// // // // // //     },
// // // // // //     {
// // // // // //       label: t.sidebar?.orders || "Orders",
// // // // // //       icon: FolderKanban,
// // // // // //       isSubmenu: true,
// // // // // //       open: ordersOpen,
// // // // // //       setOpen: setOrdersOpen,
// // // // // //       activePaths: ["/OrdersT", "/kot"],
// // // // // //       submenu: [
// // // // // //         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
// // // // // //         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
// // // // // //       ],
// // // // // //     },
// // // // // //     // {
// // // // // //     //   label: t.sidebar?.settings || "Settings",
// // // // // //     //   icon: Settings,
// // // // // //     //   isSubmenu: true,
// // // // // //     //   open: settingsOpen,
// // // // // //     //   setOpen: setSettingsOpen,
// // // // // //     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
// // // // // //     //   submenu: [
// // // // // //     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
// // // // // //     //     {
// // // // // //     //       href: "/settings/preferences",
// // // // // //     //       label: t.sidebar?.preferences || "Preferences",
// // // // // //     //       icon: "⚙️",
// // // // // //     //     },
// // // // // //     //     {
// // // // // //     //       href: "/settings/security",
// // // // // //     //       label: t.sidebar?.security || "Security",
// // // // // //     //       icon: "🔒",
// // // // // //     //     },
// // // // // //     //   ],
// // // // // //     // },
// // // // // //   ];

// // // // // //   // useEffect(() => {
// // // // // //   //   if (isOpen) {
// // // // // //   //     toggleSidebar(); // Close on route change
// // // // // //   //   }
// // // // // //   // }, [pathname]);

// // // // // //   // useEffect(() => {
// // // // // //   //   if (
// // // // // //   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
// // // // // //   //       pathname.startsWith(p)
// // // // // //   //     )
// // // // // //   //   ) {
// // // // // //   //     setSettingsOpen(false);
// // // // // //   //   }
// // // // // //   // }, [pathname]);

// // // // // //   return (
// // // // // //     <>
// // // // // //       {/* Overlay for mobile */}
// // // // // //       <div
// // // // // //         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
// // // // // //           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
// // // // // //         }`}
// // // // // //         onClick={toggleSidebar}
// // // // // //       />

// // // // // //       {/* Toggle Button */}
// // // // // //       {/* <button
// // // // // //         onClick={toggleSidebar}
// // // // // //         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
// // // // // //         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// // // // // //         type="button"
// // // // // //       >
// // // // // //         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
// // // // // //       </button> */}

// // // // // //       {/* Sidebar content */}
// // // // // //       <div
// // // // // //         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
// // // // // //           isOpen ? "left-0" : "-left-64"
// // // // // //         }`}
// // // // // //       >
// // // // // //         {/* Brand */}
        
// // // // // //         {/* Branch Selector */}
// // // // // //         <div className="px-4 pt-6 pb-2">
// // // // // //           <div
// // // // // //             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
// // // // // //             onClick={() => setBranchDropdownOpen((open) => !open)}
// // // // // //           >
// // // // // //             <span className="flex items-center gap-2">
// // // // // //               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
// // // // // //                 <circle cx="12" cy="10" r="3" />
// // // // // //                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
// // // // // //               </svg>
// // // // // //               <span className="font-bold text-primary">{selectedBranch.name}</span>
// // // // // //             </span>
// // // // // //             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
// // // // // //               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
// // // // // //             </svg>
// // // // // //             {branchDropdownOpen && (
// // // // // //               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
// // // // // //                 {branches.map((branch) => (
// // // // // //                   <div
// // // // // //                     key={branch.id}
// // // // // //                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
// // // // // //                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
// // // // // //                     onClick={() => {
// // // // // //                       setSelectedBranch(branch);
// // // // // //                       setBranchDropdownOpen(false);
// // // // // //                     }}
// // // // // //                   >
// // // // // //                     {branch.name}
// // // // // //                   </div>
// // // // // //                 ))}
// // // // // //               </div>
// // // // // //             )}
// // // // // //           </div>
// // // // // //         </div>

// // // // // //         {/* Menu */}
// // // // // //         <div className="flex-1 overflow-y-auto  pt-5">
// // // // // //           {sidebarMenu.map((item, idx) => {
// // // // // //             if (!item.isSubmenu) {
// // // // // //               const Icon = item.icon;
// // // // // //               return (
// // // // // //                 <Link
// // // // // //                   key={item.href}
// // // // // //                   href={item.href}
// // // // // //                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
// // // // // //                     isLinkActive(item.href)
// // // // // //                       ? "bg-primary text-white"
// // // // // //                       : "text-gray-700 hover:bg-gray-100"
// // // // // //                   }`}
// // // // // //                 >
// // // // // //                   <Icon className="h-5 w-5 mr-3" />
// // // // // //                   <span>{item.label}</span>
// // // // // //                 </Link>
// // // // // //               );
// // // // // //             }
// // // // // //             // Submenu
// // // // // //             const Icon = item.icon;
// // // // // //             return (
// // // // // //               <div className="relative" key={item.label}>
// // // // // //                 <button
// // // // // //                   type="button"
// // // // // //                   onClick={() => item.setOpen((prev) => !prev)}
// // // // // //                   aria-expanded={item.open}
// // // // // //                   aria-controls={`${item.label}-submenu`}
// // // // // //                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
// // // // // //                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
// // // // // //                       ? "border-primary bg-transparent text-primary"
// // // // // //                       : "border-transparent text-gray-700 hover:bg-gray-100"
// // // // // //                   }`}
// // // // // //                 >
// // // // // //                   <Icon className="h-5 w-5 mr-3" />
// // // // // //                   <span>{item.label}</span>
// // // // // //                   <svg
// // // // // //                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
// // // // // //                       item.open ? "rotate-90" : "rotate-0"
// // // // // //                     }`}
// // // // // //                     fill="none"
// // // // // //                     stroke="currentColor"
// // // // // //                     viewBox="0 0 24 24"
// // // // // //                   >
// // // // // //                     <path
// // // // // //                       strokeLinecap="round"
// // // // // //                       strokeLinejoin="round"
// // // // // //                       strokeWidth={2}
// // // // // //                       d="M9 5l7 7-7 7"
// // // // // //                     />
// // // // // //                   </svg>
// // // // // //                 </button>
// // // // // //                 <div
// // // // // //                   id={`${item.label}-submenu`}
// // // // // //                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ml-8 ${
// // // // // //                     item.open ? "max-h-40 py-2" : "max-h-0 py-0"
// // // // // //                   }`}
// // // // // //                   style={{
// // // // // //                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
// // // // // //                   }}
// // // // // //                 >
// // // // // //                   {item.submenu.map(({ href, label, icon }) => (
// // // // // //                     <Link
// // // // // //                       key={href}
// // // // // //                       href={href}
// // // // // //                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
// // // // // //                         isLinkActive(href)
// // // // // //                           ? "bg-primary text-white"
// // // // // //                           : "text-gray-700 hover:bg-gray-100"
// // // // // //                       }`}
// // // // // //                     >
// // // // // //                       <span className="mr-2">{icon}</span>
// // // // // //                       <span>{label}</span>
// // // // // //                     </Link>
// // // // // //                   ))}
// // // // // //                 </div>
// // // // // //               </div>
// // // // // //             );
// // // // // //           })}
// // // // // //         </div>
// // // // // //       </div>
// // // // // //     </>
// // // // // //   );
// // // // // // };

// // // // // // export default Sidebar;
// // // // // import { useEffect, useState } from "react";
// // // // // import Link from "next/link";
// // // // // import Image from "next/image";
// // // // // import { usePathname } from "next/navigation";
// // // // // import {
// // // // //   LayoutDashboard,
// // // // //   BarChart2,
// // // // //   Home,
// // // // //   AlertTriangle,
// // // // //   Users2,
// // // // //   Calendar,
// // // // //   FolderKanban,
// // // // //   Settings,
// // // // //   X,
// // // // //   Menu,
// // // // // } from "lucide-react";

// // // // // import { useSidebar } from "@/context/SidebarContext";
// // // // // import { useI18n } from "@/context/translate-api";

// // // // // const Sidebar = () => {
// // // // //   const { isOpen, toggleSidebar } = useSidebar();
// // // // //   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
// // // // //   const pathname = usePathname();
// // // // //   const { t } = useI18n();
// // // // //   const [settingsOpen, setSettingsOpen] = useState(false);
// // // // //   const [ordersOpen, setOrdersOpen] = useState(false);
// // // // //   const [menuOpen, setMenuOpen] = useState(false); // إضافة state لقائمة Menu
// // // // //   const [branches] = useState([
// // // // //     { id: 1, name: "DEGLA" },
// // // // //     { id: 2, name: "MAADI" },
// // // // //     // Add more branches as needed
// // // // //   ]);
// // // // //   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
// // // // //   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

// // // // //   const cancelLogout = () => setShowLogoutConfirm(false);

// // // // //   const isLinkActive = (path) => {
// // // // //     if (path === "/dashboard" && pathname === "/dashboard") return true;
// // // // //     return pathname.startsWith(path) && path !== "/dashboard";
// // // // //   };

// // // // //   // Dummy data for sidebar menu
// // // // //   const sidebarMenu = [
// // // // //     { href: "/dashboard", icon: Calendar, label: t.Ai },
// // // // //     // استبدال itemMenue بقائمة Menu مع القوائم الفرعية
// // // // //     {
// // // // //       label: "Menu",
// // // // //       icon: LayoutDashboard,
// // // // //       isSubmenu: true,
// // // // //       open: menuOpen,
// // // // //       setOpen: setMenuOpen,
// // // // //       activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
// // // // //       submenu: [
// // // // //         { href: "/menus", label: "Menus"},
// // // // //         { href: "/menu-items", label: "Menu Items"},
// // // // //         { href: "/item-categories", label: "Item Categories"},
// // // // //         { href: "/modefiers", label: "Modifier Groups" },
// // // // //         { href: "/itemModefiers", label: "Item Modifiers"},
// // // // //         { href: "/itemMenue", label: "itemMenue"},
// // // // //       ],
// // // // //     },
// // // // //     {
// // // // //       label: "tabels",
// // // // //       icon: LayoutDashboard,
// // // // //       isSubmenu: true,
// // // // //       open: menuOpen,
// // // // //       setOpen: setMenuOpen,
// // // // //       activePaths: ["/areas", "/tabels", "/qrcodes"],
// // // // //       submenu: [
// // // // //         { href: "/areas", label: "Areas"},
// // // // //         { href: "/tabels", label: "Tabels"},
// // // // //         { href: "/qrcodes", label: "Qr codes"},
// // // // //       ],
// // // // //     },
// // // // //     { href: "/staff", icon: FolderKanban, label: "الطاقم" },
// // // // //     { href: "/customers", icon: FolderKanban, label: "الزبائن" },
// // // // //     { href: "/pos", icon: FolderKanban, label: "pos" },
// // // // //     { href: "/Settings", icon: FolderKanban, label: "Settings" },
// // // // //     {
// // // // //       href: "/diliveryExcutive",
// // // // //       icon: FolderKanban,
// // // // //       label: "diliveryExcutive",
// // // // //     },
// // // // //     {
// // // // //       label: t.sidebar?.orders || "Orders",
// // // // //       icon: FolderKanban,
// // // // //       isSubmenu: true,
// // // // //       open: ordersOpen,
// // // // //       setOpen: setOrdersOpen,
// // // // //       activePaths: ["/OrdersT", "/kot"],
// // // // //       submenu: [
// // // // //         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
// // // // //         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
// // // // //       ],
// // // // //     },
// // // // //     // {
// // // // //     //   label: t.sidebar?.settings || "Settings",
// // // // //     //   icon: Settings,
// // // // //     //   isSubmenu: true,
// // // // //     //   open: settingsOpen,
// // // // //     //   setOpen: setSettingsOpen,
// // // // //     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
// // // // //     //   submenu: [
// // // // //     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
// // // // //     //     {
// // // // //     //       href: "/settings/preferences",
// // // // //     //       label: t.sidebar?.preferences || "Preferences",
// // // // //     //       icon: "⚙️",
// // // // //     //     },
// // // // //     //     {
// // // // //     //       href: "/settings/security",
// // // // //     //       label: t.sidebar?.security || "Security",
// // // // //     //       icon: "🔒",
// // // // //     //     },
// // // // //     //   ],
// // // // //     // },
// // // // //   ];

// // // // //   // useEffect(() => {
// // // // //   //   if (isOpen) {
// // // // //   //     toggleSidebar(); // Close on route change
// // // // //   //   }
// // // // //   // }, [pathname]);

// // // // //   // useEffect(() => {
// // // // //   //   if (
// // // // //   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
// // // // //   //       pathname.startsWith(p)
// // // // //   //     )
// // // // //   //   ) {
// // // // //   //     setSettingsOpen(false);
// // // // //   //   }
// // // // //   // }, [pathname]);

// // // // //   return (
// // // // //     <>
// // // // //       {/* Overlay for mobile */}
// // // // //       <div
// // // // //         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
// // // // //           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
// // // // //         }`}
// // // // //         onClick={toggleSidebar}
// // // // //       />

// // // // //       {/* Toggle Button */}
// // // // //       {/* <button
// // // // //         onClick={toggleSidebar}
// // // // //         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
// // // // //         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// // // // //         type="button"
// // // // //       >
// // // // //         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
// // // // //       </button> */}

// // // // //       {/* Sidebar content */}
// // // // //       <div
// // // // //         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
// // // // //           isOpen ? "left-0" : "-left-64"
// // // // //         }`}
// // // // //       >
// // // // //         {/* Brand */}
        
// // // // //         {/* Branch Selector */}
// // // // //         <div className="px-4 pt-6 pb-2">
// // // // //           <div
// // // // //             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
// // // // //             onClick={() => setBranchDropdownOpen((open) => !open)}
// // // // //           >
// // // // //             <span className="flex items-center gap-2">
// // // // //               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
// // // // //                 <circle cx="12" cy="10" r="3" />
// // // // //                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
// // // // //               </svg>
// // // // //               <span className="font-bold text-primary">{selectedBranch.name}</span>
// // // // //             </span>
// // // // //             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
// // // // //               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
// // // // //             </svg>
// // // // //             {branchDropdownOpen && (
// // // // //               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
// // // // //                 {branches.map((branch) => (
// // // // //                   <div
// // // // //                     key={branch.id}
// // // // //                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
// // // // //                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
// // // // //                     onClick={() => {
// // // // //                       setSelectedBranch(branch);
// // // // //                       setBranchDropdownOpen(false);
// // // // //                     }}
// // // // //                   >
// // // // //                     {branch.name}
// // // // //                   </div>
// // // // //                 ))}
// // // // //               </div>
// // // // //             )}
// // // // //           </div>
// // // // //         </div>

// // // // //         {/* Menu */}
// // // // //         <div className="flex-1 overflow-y-auto  pt-5">
// // // // //           {sidebarMenu.map((item, idx) => {
// // // // //             if (!item.isSubmenu) {
// // // // //               const Icon = item.icon;
// // // // //               return (
// // // // //                 <Link
// // // // //                   key={item.href}
// // // // //                   href={item.href}
// // // // //                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
// // // // //                     isLinkActive(item.href)
// // // // //                       ? "bg-primary text-white"
// // // // //                       : "text-gray-700 hover:bg-gray-100"
// // // // //                   }`}
// // // // //                 >
// // // // //                   <Icon className="h-5 w-5 mr-3" />
// // // // //                   <span>{item.label}</span>
// // // // //                 </Link>
// // // // //               );
// // // // //             }
// // // // //             // Submenu
// // // // //             const Icon = item.icon;
// // // // //             return (
// // // // //               <div className="relative" key={item.label}>
// // // // //                 <button
// // // // //                   type="button"
// // // // //                   onClick={() => item.setOpen((prev) => !prev)}
// // // // //                   aria-expanded={item.open}
// // // // //                   aria-controls={`${item.label}-submenu`}
// // // // //                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
// // // // //                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
// // // // //                       ? "border-primary bg-transparent text-primary"
// // // // //                       : "border-transparent text-gray-700 hover:bg-gray-100"
// // // // //                   }`}
// // // // //                 >
// // // // //                   <Icon className="h-5 w-5 mr-3" />
// // // // //                   <span>{item.label}</span>
// // // // //                   <svg
// // // // //                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
// // // // //                       item.open ? "rotate-90" : "rotate-0"
// // // // //                     }`}
// // // // //                     fill="none"
// // // // //                     stroke="currentColor"
// // // // //                     viewBox="0 0 24 24"
// // // // //                   >
// // // // //                     <path
// // // // //                       strokeLinecap="round"
// // // // //                       strokeLinejoin="round"
// // // // //                       strokeWidth={2}
// // // // //                       d="M9 5l7 7-7 7"
// // // // //                     />
// // // // //                   </svg>
// // // // //                 </button>
// // // // //                 <div
// // // // //                   id={`${item.label}-submenu`}
// // // // //                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ${
// // // // //                     item.open ? "max-h-60 py-2" : "max-h-0 py-0"
// // // // //                   }`}
// // // // //                   style={{
// // // // //                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
// // // // //                   }}
// // // // //                 >
// // // // //                   {item.submenu.map(({ href, label, icon }) => (
// // // // //                     <Link
// // // // //                       key={href}
// // // // //                       href={href}
// // // // //                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
// // // // //                         isLinkActive(href)
// // // // //                           ? "bg-primary text-white"
// // // // //                           : "text-gray-700 hover:bg-gray-100"
// // // // //                       }`}
// // // // //                     >
// // // // //                       <span className="mr-2">{icon}</span>
// // // // //                       <span>{label}</span>
// // // // //                     </Link>
// // // // //                   ))}
// // // // //                 </div>
// // // // //               </div>
// // // // //             );
// // // // //           })}
// // // // //         </div>
// // // // //       </div>
// // // // //     </>
// // // // //   );
// // // // // };

// // // // // export default Sidebar;
// // // // import { useEffect, useState } from "react";
// // // // import Link from "next/link";
// // // // import Image from "next/image";
// // // // import { usePathname } from "next/navigation";
// // // // import {
// // // //   LayoutDashboard,
// // // //   BarChart2,
// // // //   Home,
// // // //   AlertTriangle,
// // // //   Users2,
// // // //   Calendar,
// // // //   FolderKanban,
// // // //   Settings,
// // // //   X,
// // // //   Menu,
// // // // } from "lucide-react";

// // // // import { useSidebar } from "@/context/SidebarContext";
// // // // import { useI18n } from "@/context/translate-api";

// // // // const Sidebar = () => {
// // // //   const { isOpen, toggleSidebar } = useSidebar();
// // // //   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
// // // //   const pathname = usePathname();
// // // //   const { t } = useI18n();
// // // //   const [settingsOpen, setSettingsOpen] = useState(false);
// // // //   const [ordersOpen, setOrdersOpen] = useState(false);
// // // //   const [menuOpen, setMenuOpen] = useState(false); // للقائمة الأولى Menu
// // // //   const [tablesOpen, setTablesOpen] = useState(false); // للقائمة الثانية Tables
// // // //   const [branches] = useState([
// // // //     { id: 1, name: "DEGLA" },
// // // //     { id: 2, name: "MAADI" },
// // // //     // Add more branches as needed
// // // //   ]);
// // // //   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
// // // //   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

// // // //   const cancelLogout = () => setShowLogoutConfirm(false);

// // // //   const isLinkActive = (path) => {
// // // //     if (path === "/dashboard" && pathname === "/dashboard") return true;
// // // //     return pathname.startsWith(path) && path !== "/dashboard";
// // // //   };

// // // //   // Dummy data for sidebar menu
// // // //   const sidebarMenu = [
// // // //     { href: "/dashboard", icon: Calendar, label: "Auto Reply" },
// // // //     // قائمة Menu مع state منفصل
// // // //     {
// // // //       label: "Menu",
// // // //       icon: LayoutDashboard,
// // // //       isSubmenu: true,
// // // //       open: menuOpen,
// // // //       setOpen: setMenuOpen,
// // // //       activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
// // // //       submenu: [
// // // //         { href: "/menus", label: "Menus"},
// // // //         { href: "/menu-items", label: "Menu Items"},
// // // //         { href: "/item-categories", label: "Item Categories"},
// // // //         { href: "/modefiers", label: "Modifier Groups" },
// // // //         { href: "/itemModefiers", label: "Item Modifiers"},
// // // //         { href: "/itemMenue", label: "itemMenue"},
// // // //       ],
// // // //     },
// // // //     // قائمة Tables مع state منفصل
// // // //     {
// // // //       label: "tabels",
// // // //       icon: FolderKanban,
// // // //       isSubmenu: true,
// // // //       open: tablesOpen,
// // // //       setOpen: setTablesOpen,
// // // //       activePaths: ["/areas", "/tabels", "/qrcodes"],
// // // //       submenu: [
// // // //         { href: "/areas", label: "Areas"},
// // // //         { href: "/tabels", label: "Tabels"},
// // // //         { href: "/qrcodes", label: "Qr codes"},
// // // //       ],
// // // //     },
// // // //     { href: "/staff", icon: FolderKanban, label: "Staff" },
// // // //     { href: "/customers", icon: FolderKanban, label: "Customers" },
// // // //     { href: "/pos", icon: FolderKanban, label: "pos" },
// // // //     {
// // // //       label: t.sidebar?.orders || "Orders",
// // // //       icon: FolderKanban,
// // // //       isSubmenu: true,
// // // //       open: ordersOpen,
// // // //       setOpen: setOrdersOpen,
// // // //       activePaths: ["/OrdersT", "/kot"],
// // // //       submenu: [
// // // //         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
// // // //         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
// // // //       ],
// // // //     },
// // // //     {
// // // //       href: "/diliveryExcutive",
// // // //       icon: FolderKanban,
// // // //       label: "delivery",
// // // //     },

// // // //     { href: "/Settings", icon: FolderKanban, label: "Settings" },

// // // //     // {
// // // //     //   label: t.sidebar?.settings || "Settings",
// // // //     //   icon: Settings,
// // // //     //   isSubmenu: true,
// // // //     //   open: settingsOpen,
// // // //     //   setOpen: setSettingsOpen,
// // // //     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
// // // //     //   submenu: [
// // // //     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
// // // //     //     {
// // // //     //       href: "/settings/preferences",
// // // //     //       label: t.sidebar?.preferences || "Preferences",
// // // //     //       icon: "⚙️",
// // // //     //     },
// // // //     //     {
// // // //     //       href: "/settings/security",
// // // //     //       label: t.sidebar?.security || "Security",
// // // //     //       icon: "🔒",
// // // //     //     },
// // // //     //   ],
// // // //     // },
// // // //   ];

// // // //   // useEffect(() => {
// // // //   //   if (isOpen) {
// // // //   //     toggleSidebar(); // Close on route change
// // // //   //   }
// // // //   // }, [pathname]);

// // // //   // useEffect(() => {
// // // //   //   if (
// // // //   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
// // // //   //       pathname.startsWith(p)
// // // //   //     )
// // // //   //   ) {
// // // //   //     setSettingsOpen(false);
// // // //   //   }
// // // //   // }, [pathname]);

// // // //   return (
// // // //     <>
// // // //       {/* Overlay for mobile */}
// // // //       <div
// // // //         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
// // // //           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
// // // //         }`}
// // // //         onClick={toggleSidebar}
// // // //       />

// // // //       {/* Toggle Button */}
// // // //       {/* <button
// // // //         onClick={toggleSidebar}
// // // //         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
// // // //         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// // // //         type="button"
// // // //       >
// // // //         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
// // // //       </button> */}

// // // //       {/* Sidebar content */}
// // // //       <div
// // // //         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
// // // //           isOpen ? "left-0" : "-left-64"
// // // //         }`}
// // // //       >
// // // //         {/* Brand */}
        
// // // //         {/* Branch Selector */}
// // // //         <div className="px-4 pt-6 pb-2">
// // // //           <div
// // // //             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
// // // //             onClick={() => setBranchDropdownOpen((open) => !open)}
// // // //           >
// // // //             <span className="flex items-center gap-2">
// // // //               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
// // // //                 <circle cx="12" cy="10" r="3" />
// // // //                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
// // // //               </svg>
// // // //               <span className="font-bold text-primary">{selectedBranch.name}</span>
// // // //             </span>
// // // //             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
// // // //               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
// // // //             </svg>
// // // //             {branchDropdownOpen && (
// // // //               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
// // // //                 {branches.map((branch) => (
// // // //                   <div
// // // //                     key={branch.id}
// // // //                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
// // // //                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
// // // //                     onClick={() => {
// // // //                       setSelectedBranch(branch);
// // // //                       setBranchDropdownOpen(false);
// // // //                     }}
// // // //                   >
// // // //                     {branch.name}
// // // //                   </div>
// // // //                 ))}
// // // //               </div>
// // // //             )}
// // // //           </div>
// // // //         </div>

// // // //         {/* Menu */}
// // // //         <div className="flex-1 overflow-y-auto  pt-5">
// // // //           {sidebarMenu.map((item, idx) => {
// // // //             if (!item.isSubmenu) {
// // // //               const Icon = item.icon;
// // // //               return (
// // // //                 <Link
// // // //                   key={item.href}
// // // //                   href={item.href}
// // // //                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
// // // //                     isLinkActive(item.href)
// // // //                       ? "bg-primary text-white"
// // // //                       : "text-gray-700 hover:bg-gray-100"
// // // //                   }`}
// // // //                 >
// // // //                   <Icon className="h-5 w-5 mr-3" />
// // // //                   <span>{item.label}</span>
// // // //                 </Link>
// // // //               );
// // // //             }
// // // //             // Submenu
// // // //             const Icon = item.icon;
// // // //             return (
// // // //               <div className="relative" key={item.label}>
// // // //                 <button
// // // //                   type="button"
// // // //                   onClick={() => item.setOpen((prev) => !prev)}
// // // //                   aria-expanded={item.open}
// // // //                   aria-controls={`${item.label}-submenu`}
// // // //                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
// // // //                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
// // // //                       ? "border-primary bg-transparent text-primary"
// // // //                       : "border-transparent text-gray-700 hover:bg-gray-100"
// // // //                   }`}
// // // //                 >
// // // //                   <Icon className="h-5 w-5 mr-3" />
// // // //                   <span>{item.label}</span>
// // // //                   <svg
// // // //                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
// // // //                       item.open ? "rotate-90" : "rotate-0"
// // // //                     }`}
// // // //                     fill="none"
// // // //                     stroke="currentColor"
// // // //                     viewBox="0 0 24 24"
// // // //                   >
// // // //                     <path
// // // //                       strokeLinecap="round"
// // // //                       strokeLinejoin="round"
// // // //                       strokeWidth={2}
// // // //                       d="M9 5l7 7-7 7"
// // // //                     />
// // // //                   </svg>
// // // //                 </button>
// // // //                 <div
// // // //                   id={`${item.label}-submenu`}
// // // //                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ${
// // // //                     item.open ? "max-h-60 py-2" : "max-h-0 py-0"
// // // //                   }`}
// // // //                   style={{
// // // //                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
// // // //                   }}
// // // //                 >
// // // //                   {item.submenu.map(({ href, label, icon }) => (
// // // //                     <Link
// // // //                       key={href}
// // // //                       href={href}
// // // //                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
// // // //                         isLinkActive(href)
// // // //                           ? "bg-primary text-white"
// // // //                           : "text-gray-700 hover:bg-gray-100"
// // // //                       }`}
// // // //                     >
// // // //                       <span className="mr-2">{icon}</span>
// // // //                       <span>{label}</span>
// // // //                     </Link>
// // // //                   ))}
// // // //                 </div>
// // // //               </div>
// // // //             );
// // // //           })}
// // // //         </div>
// // // //       </div>
// // // //     </>
// // // //   );
// // // // };

// // // // export default Sidebar;
// // // import { useEffect, useState } from "react";
// // // import Link from "next/link";
// // // import Image from "next/image";
// // // import { usePathname } from "next/navigation";
// // // import {
// // //   LayoutDashboard,
// // //   BarChart2,
// // //   Home,
// // //   AlertTriangle,
// // //   Users2,
// // //   Calendar,
// // //   FolderKanban,
// // //   Settings,
// // //   X,
// // //   Menu,
// // //   MessageCircle,
// // //   UtensilsCrossed,
// // //   Grid3x3,
// // //   Layers,
// // //   Sliders,
// // //   PlusCircle,
// // //   MapPin,
// // //   QrCode,
// // //   UserCheck,
// // //   Users,
// // //   ShoppingCart,
// // //   ClipboardList,
// // //   ChefHat,
// // //   Truck,
// // //   Cog
// // // } from "lucide-react";

// // // import { useSidebar } from "@/context/SidebarContext";
// // // import { useI18n } from "@/context/translate-api";

// // // const Sidebar = () => {
// // //   const { isOpen, toggleSidebar } = useSidebar();
// // //   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
// // //   const pathname = usePathname();
// // //   const { t } = useI18n();
// // //   const [settingsOpen, setSettingsOpen] = useState(false);
// // //   const [ordersOpen, setOrdersOpen] = useState(false);
// // //   const [menuOpen, setMenuOpen] = useState(false); // للقائمة الأولى Menu
// // //   const [tablesOpen, setTablesOpen] = useState(false); // للقائمة الثانية Tables
// // //   const [branches] = useState([
// // //     { id: 1, name: "DEGLA" },
// // //     { id: 2, name: "MAADI" },
// // //     // Add more branches as needed
// // //   ]);
// // //   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
// // //   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

// // //   const cancelLogout = () => setShowLogoutConfirm(false);

// // //   const isLinkActive = (path) => {
// // //     if (path === "/dashboard" && pathname === "/dashboard") return true;
// // //     return pathname.startsWith(path) && path !== "/dashboard";
// // //   };

// // //   // Dummy data for sidebar menu
// // //   const sidebarMenu = [
// // //     { href: "/dashboard", icon: MessageCircle, label: "Auto Reply" },
// // //     // قائمة Menu مع state منفصل
// // //     {
// // //       label: "Menu",
// // //       icon: UtensilsCrossed,
// // //       isSubmenu: true,
// // //       open: menuOpen,
// // //       setOpen: setMenuOpen,
// // //       activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
// // //       submenu: [
// // //         { href: "/menus", label: "Menus", icon: "📋"},
// // //         { href: "/menu-items", label: "Menu Items", icon: "🍽️"},
// // //         { href: "/item-categories", label: "Item Categories", icon: "📂"},
// // //         { href: "/modefiers", label: "Modifier Groups", icon: "🔧" },
// // //         { href: "/itemModefiers", label: "Item Modifiers", icon: "⚙️"},
// // //         { href: "/itemMenue", label: "itemMenue", icon: "📝"},
// // //       ],
// // //     },
// // //     // قائمة Tables مع state منفصل
// // //     {
// // //       label: "tabels",
// // //       icon: Grid3x3,
// // //       isSubmenu: true,
// // //       open: tablesOpen,
// // //       setOpen: setTablesOpen,
// // //       activePaths: ["/areas", "/tabels", "/qrcodes"],
// // //       submenu: [
// // //         { href: "/areas", label: "Areas", icon: "🏢"},
// // //         { href: "/tabels", label: "Tabels", icon: "🪑"},
// // //         { href: "/qrcodes", label: "Qr codes", icon: "📱"},
// // //       ],
// // //     },
// // //     { href: "/staff", icon: UserCheck, label: "Staff" },
// // //     { href: "/customers", icon: Users, label: "Customers" },
// // //     { href: "/pos", icon: ShoppingCart, label: "pos" },
// // //     {
// // //       label: t.sidebar?.orders || "Orders",
// // //       icon: ClipboardList,
// // //       isSubmenu: true,
// // //       open: ordersOpen,
// // //       setOpen: setOrdersOpen,
// // //       activePaths: ["/OrdersT", "/kot"],
// // //       submenu: [
// // //         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
// // //         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
// // //       ],
// // //     },
// // //     {
// // //       href: "/diliveryExcutive",
// // //       icon: Truck,
// // //       label: "delivery",
// // //     },

// // //     { href: "/Settings", icon: Cog, label: "Settings" },

// // //     // {
// // //     //   label: t.sidebar?.settings || "Settings",
// // //     //   icon: Settings,
// // //     //   isSubmenu: true,
// // //     //   open: settingsOpen,
// // //     //   setOpen: setSettingsOpen,
// // //     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
// // //     //   submenu: [
// // //     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
// // //     //     {
// // //     //       href: "/settings/preferences",
// // //     //       label: t.sidebar?.preferences || "Preferences",
// // //     //       icon: "⚙️",
// // //     //     },
// // //     //     {
// // //     //       href: "/settings/security",
// // //     //       label: t.sidebar?.security || "Security",
// // //     //       icon: "🔒",
// // //     //     },
// // //     //   ],
// // //     // },
// // //   ];

// // //   // useEffect(() => {
// // //   //   if (isOpen) {
// // //   //     toggleSidebar(); // Close on route change
// // //   //   }
// // //   // }, [pathname]);

// // //   // useEffect(() => {
// // //   //   if (
// // //   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
// // //   //       pathname.startsWith(p)
// // //   //     )
// // //   //   ) {
// // //   //     setSettingsOpen(false);
// // //   //   }
// // //   // }, [pathname]);

// // //   return (
// // //     <>
// // //       {/* Overlay for mobile */}
// // //       <div
// // //         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
// // //           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
// // //         }`}
// // //         onClick={toggleSidebar}
// // //       />

// // //       {/* Toggle Button */}
// // //       {/* <button
// // //         onClick={toggleSidebar}
// // //         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
// // //         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// // //         type="button"
// // //       >
// // //         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
// // //       </button> */}

// // //       {/* Sidebar content */}
// // //       <div
// // //         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
// // //           isOpen ? "left-0" : "-left-64"
// // //         }`}
// // //       >
// // //         {/* Brand */}
        
// // //         {/* Branch Selector */}
// // //         <div className="px-4 pt-6 pb-2">
// // //           <div
// // //             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
// // //             onClick={() => setBranchDropdownOpen((open) => !open)}
// // //           >
// // //             <span className="flex items-center gap-2">
// // //               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
// // //                 <circle cx="12" cy="10" r="3" />
// // //                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
// // //               </svg>
// // //               <span className="font-bold text-primary">{selectedBranch.name}</span>
// // //             </span>
// // //             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
// // //               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
// // //             </svg>
// // //             {branchDropdownOpen && (
// // //               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
// // //                 {branches.map((branch) => (
// // //                   <div
// // //                     key={branch.id}
// // //                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
// // //                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
// // //                     onClick={() => {
// // //                       setSelectedBranch(branch);
// // //                       setBranchDropdownOpen(false);
// // //                     }}
// // //                   >
// // //                     {branch.name}
// // //                   </div>
// // //                 ))}
// // //               </div>
// // //             )}
// // //           </div>
// // //         </div>

// // //         {/* Menu */}
// // //         <div className="flex-1 overflow-y-auto  pt-5">
// // //           {sidebarMenu.map((item, idx) => {
// // //             if (!item.isSubmenu) {
// // //               const Icon = item.icon;
// // //               return (
// // //                 <Link
// // //                   key={item.href}
// // //                   href={item.href}
// // //                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
// // //                     isLinkActive(item.href)
// // //                       ? "bg-primary text-white"
// // //                       : "text-gray-700 hover:bg-gray-100"
// // //                   }`}
// // //                 >
// // //                   <Icon className="h-5 w-5 mr-3" />
// // //                   <span>{item.label}</span>
// // //                 </Link>
// // //               );
// // //             }
// // //             // Submenu
// // //             const Icon = item.icon;
// // //             return (
// // //               <div className="relative" key={item.label}>
// // //                 <button
// // //                   type="button"
// // //                   onClick={() => item.setOpen((prev) => !prev)}
// // //                   aria-expanded={item.open}
// // //                   aria-controls={`${item.label}-submenu`}
// // //                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
// // //                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
// // //                       ? "border-primary bg-transparent text-primary"
// // //                       : "border-transparent text-gray-700 hover:bg-gray-100"
// // //                   }`}
// // //                 >
// // //                   <Icon className="h-5 w-5 mr-3" />
// // //                   <span>{item.label}</span>
// // //                   <svg
// // //                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
// // //                       item.open ? "rotate-90" : "rotate-0"
// // //                     }`}
// // //                     fill="none"
// // //                     stroke="currentColor"
// // //                     viewBox="0 0 24 24"
// // //                   >
// // //                     <path
// // //                       strokeLinecap="round"
// // //                       strokeLinejoin="round"
// // //                       strokeWidth={2}
// // //                       d="M9 5l7 7-7 7"
// // //                     />
// // //                   </svg>
// // //                 </button>
// // //                 <div
// // //                   id={`${item.label}-submenu`}
// // //                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ${
// // //                     item.open ? "max-h-60 py-2" : "max-h-0 py-0"
// // //                   }`}
// // //                   style={{
// // //                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
// // //                   }}
// // //                 >
// // //                   {item.submenu.map(({ href, label, icon }) => (
// // //                     <Link
// // //                       key={href}
// // //                       href={href}
// // //                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
// // //                         isLinkActive(href)
// // //                           ? "bg-primary text-white"
// // //                           : "text-gray-700 hover:bg-gray-100"
// // //                       }`}
// // //                     >
// // //                       <span className="mr-2">{icon}</span>
// // //                       <span>{label}</span>
// // //                     </Link>
// // //                   ))}
// // //                 </div>
// // //               </div>
// // //             );
// // //           })}
// // //         </div>
// // //       </div>
// // //     </>
// // //   );
// // // };

// // // export default Sidebar;
// // import { useEffect, useState } from "react";
// // import Link from "next/link";
// // import Image from "next/image";
// // import { usePathname } from "next/navigation";
// // import {
// //   LayoutDashboard,
// //   BarChart2,
// //   Home,
// //   AlertTriangle,
// //   Users2,
// //   Calendar,
// //   FolderKanban,
// //   Settings,
// //   X,
// //   Menu,
// //   MessageCircle,
// //   UtensilsCrossed,
// //   Grid3x3,
// //   Layers,
// //   Sliders,
// //   PlusCircle,
// //   MapPin,
// //   QrCode,
// //   UserCheck,
// //   Users,
// //   ShoppingCart,
// //   ClipboardList,
// //   ChefHat,
// //   Truck,
// //   Cog,
// //   Utensils
// // } from "lucide-react";

// // import { useSidebar } from "@/context/SidebarContext";
// // import { useI18n } from "@/context/translate-api";

// // const Sidebar = () => {
// //   const { isOpen, toggleSidebar } = useSidebar();
// //   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
// //   const pathname = usePathname();
// //   const { t } = useI18n();
// //   const [settingsOpen, setSettingsOpen] = useState(false);
// //   const [ordersOpen, setOrdersOpen] = useState(false);
// //   const [menuOpen, setMenuOpen] = useState(false); // للقائمة الأولى Menu
// //   const [tablesOpen, setTablesOpen] = useState(false); // للقائمة الثانية Tables
// //   const [branches] = useState([
// //     { id: 1, name: "DEGLA" },
// //     { id: 2, name: "MAADI" },
// //     // Add more branches as needed
// //   ]);
// //   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
// //   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

// //   const cancelLogout = () => setShowLogoutConfirm(false);

// //   const isLinkActive = (path) => {
// //     if (path === "/dashboard" && pathname === "/dashboard") return true;
// //     return pathname.startsWith(path) && path !== "/dashboard";
// //   };

// //   // Dummy data for sidebar menu
// //   const sidebarMenu = [
// //     { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
// //     // قائمة Menu مع state منفصل
// //     {
// //       label: "Menu",
// //       icon: UtensilsCrossed,
// //       isSubmenu: true,
// //       open: menuOpen,
// //       setOpen: setMenuOpen,
// //       activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
// //       submenu: [
// //         { href: "/menus", label: "Menus", icon: "📋"},
// //         { href: "/menu-items", label: "Menu Items", icon: "🍽️"},
// //         { href: "/item-categories", label: "Item Categories", icon: "📂"},
// //         { href: "/modefiers", label: "Modifier Groups", icon: "🔧" },
// //         { href: "/itemModefiers", label: "Item Modifiers", icon: "⚙️"},
// //         { href: "/itemMenue", label: "itemMenue", icon: "📝"},
// //       ],
// //     },
// //     // قائمة Tables مع state منفصل
// //     {
// //       label: "tabels",
// //       icon: Utensils,
// //       isSubmenu: true,
// //       open: tablesOpen,
// //       setOpen: setTablesOpen,
// //       activePaths: ["/areas", "/tabels", "/qrcodes"],
// //       submenu: [
// //         { href: "/areas", label: "Areas", icon: "🏢"},
// //         { href: "/tabels", label: "Tabels", icon: "🪑"},
// //         { href: "/qrcodes", label: "Qr codes", icon: "📱"},
// //       ],
// //     },
// //     { href: "/staff", icon: UserCheck, label: "Staff" },
// //     { href: "/customers", icon: Users, label: "Customers" },
// //     { href: "/pos", icon: ShoppingCart, label: "pos" },
// //     {
// //       label: t.sidebar?.orders || "Orders",
// //       icon: ClipboardList,
// //       isSubmenu: true,
// //       open: ordersOpen,
// //       setOpen: setOrdersOpen,
// //       activePaths: ["/OrdersT", "/kot"],
// //       submenu: [
// //         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
// //         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
// //       ],
// //     },
// //     {
// //       href: "/diliveryExcutive",
// //       icon: Truck,
// //       label: "delivery",
// //     },

// //     { href: "/Settings", icon: Cog, label: "Settings" },

// //     // {
// //     //   label: t.sidebar?.settings || "Settings",
// //     //   icon: Settings,
// //     //   isSubmenu: true,
// //     //   open: settingsOpen,
// //     //   setOpen: setSettingsOpen,
// //     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
// //     //   submenu: [
// //     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
// //     //     {
// //     //       href: "/settings/preferences",
// //     //       label: t.sidebar?.preferences || "Preferences",
// //     //       icon: "⚙️",
// //     //     },
// //     //     {
// //     //       href: "/settings/security",
// //     //       label: t.sidebar?.security || "Security",
// //     //       icon: "🔒",
// //     //     },
// //     //   ],
// //     // },
// //   ];

// //   // useEffect(() => {
// //   //   if (isOpen) {
// //   //     toggleSidebar(); // Close on route change
// //   //   }
// //   // }, [pathname]);

// //   // useEffect(() => {
// //   //   if (
// //   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
// //   //       pathname.startsWith(p)
// //   //     )
// //   //   ) {
// //   //     setSettingsOpen(false);
// //   //   }
// //   // }, [pathname]);

// //   return (
// //     <>
// //       {/* Overlay for mobile */}
// //       <div
// //         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
// //           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
// //         }`}
// //         onClick={toggleSidebar}
// //       />

// //       {/* Toggle Button */}
// //       {/* <button
// //         onClick={toggleSidebar}
// //         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
// //         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// //         type="button"
// //       >
// //         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
// //       </button> */}

// //       {/* Sidebar content */}
// //       <div
// //         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
// //           isOpen ? "left-0" : "-left-64"
// //         }`}
// //       >
// //         {/* Brand */}
        
// //         {/* Branch Selector */}
// //         <div className="px-4 pt-6 pb-2">
// //           <div
// //             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
// //             onClick={() => setBranchDropdownOpen((open) => !open)}
// //           >
// //             <span className="flex items-center gap-2">
// //               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
// //                 <circle cx="12" cy="10" r="3" />
// //                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
// //               </svg>
// //               <span className="font-bold text-primary">{selectedBranch.name}</span>
// //             </span>
// //             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
// //               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
// //             </svg>
// //             {branchDropdownOpen && (
// //               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
// //                 {branches.map((branch) => (
// //                   <div
// //                     key={branch.id}
// //                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
// //                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
// //                     onClick={() => {
// //                       setSelectedBranch(branch);
// //                       setBranchDropdownOpen(false);
// //                     }}
// //                   >
// //                     {branch.name}
// //                   </div>
// //                 ))}
// //               </div>
// //             )}
// //           </div>
// //         </div>

// //         {/* Menu */}
// //         <div className="flex-1 overflow-y-auto  pt-5">
// //           {sidebarMenu.map((item, idx) => {
// //             if (!item.isSubmenu) {
// //               const Icon = item.icon;
// //               return (
// //                 <Link
// //                   key={item.href}
// //                   href={item.href}
// //                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
// //                     isLinkActive(item.href)
// //                       ? "bg-primary text-white"
// //                       : "text-gray-700 hover:bg-gray-100"
// //                   }`}
// //                 >
// //                   <Icon className="h-5 w-5 mr-3" />
// //                   <span>{item.label}</span>
// //                 </Link>
// //               );
// //             }
// //             // Submenu
// //             const Icon = item.icon;
// //             return (
// //               <div className="relative" key={item.label}>
// //                 <button
// //                   type="button"
// //                   onClick={() => item.setOpen((prev) => !prev)}
// //                   aria-expanded={item.open}
// //                   aria-controls={`${item.label}-submenu`}
// //                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
// //                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
// //                       ? "border-primary bg-transparent text-primary"
// //                       : "border-transparent text-gray-700 hover:bg-gray-100"
// //                   }`}
// //                 >
// //                   <Icon className="h-5 w-5 mr-3" />
// //                   <span>{item.label}</span>
// //                   <svg
// //                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
// //                       item.open ? "rotate-90" : "rotate-0"
// //                     }`}
// //                     fill="none"
// //                     stroke="currentColor"
// //                     viewBox="0 0 24 24"
// //                   >
// //                     <path
// //                       strokeLinecap="round"
// //                       strokeLinejoin="round"
// //                       strokeWidth={2}
// //                       d="M9 5l7 7-7 7"
// //                     />
// //                   </svg>
// //                 </button>
// //                 <div
// //                   id={`${item.label}-submenu`}
// //                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ${
// //                     item.open ? "max-h-60 py-2" : "max-h-0 py-0"
// //                   }`}
// //                   style={{
// //                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
// //                   }}
// //                 >
// //                   {item.submenu.map(({ href, label, icon }) => (
// //                     <Link
// //                       key={href}
// //                       href={href}
// //                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
// //                         isLinkActive(href)
// //                           ? "bg-primary text-white"
// //                           : "text-gray-700 hover:bg-gray-100"
// //                       }`}
// //                     >
// //                       <span className="mr-2">{icon}</span>
// //                       <span>{label}</span>
// //                     </Link>
// //                   ))}
// //                 </div>
// //               </div>
// //             );
// //           })}
// //         </div>
// //       </div>
// //     </>
// //   );
// // };

// // export default Sidebar;
// import { useEffect, useState } from "react";
// import Link from "next/link";
// import Image from "next/image";
// import { usePathname } from "next/navigation";
// import {
//   LayoutDashboard,
//   BarChart2,
//   Home,
//   AlertTriangle,
//   Users2,
//   Calendar,
//   FolderKanban,
//   Settings,
//   X,
//   Menu,
//   MessageCircle,
//   UtensilsCrossed,
//   Grid3x3,
//   Layers,
//   Sliders,
//   PlusCircle,
//   MapPin,
//   QrCode,
//   UserCheck,
//   Users,
//   ShoppingCart,
//   ClipboardList,
//   ChefHat,
//   Truck,
//   Cog,
//   HandPlatter,
//   Table
// } from "lucide-react";

// import { useSidebar } from "@/context/SidebarContext";
// import { useI18n } from "@/context/translate-api";

// const Sidebar = () => {
//   const { isOpen, toggleSidebar } = useSidebar();
//   const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
//   const pathname = usePathname();
//   const { t } = useI18n();
//   const [settingsOpen, setSettingsOpen] = useState(false);
//   const [ordersOpen, setOrdersOpen] = useState(false);
//   const [menuOpen, setMenuOpen] = useState(false); // للقائمة الأولى Menu
//   const [tablesOpen, setTablesOpen] = useState(false); // للقائمة الثانية Tables
//   const [branches] = useState([
//     { id: 1, name: "DEGLA" },
//     { id: 2, name: "MAADI" },
//     // Add more branches as needed
//   ]);
//   const [selectedBranch, setSelectedBranch] = useState(branches[0]);
//   const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

//   const cancelLogout = () => setShowLogoutConfirm(false);

//   const isLinkActive = (path) => {
//     if (path === "/dashboard" && pathname === "/dashboard") return true;
//     return pathname.startsWith(path) && path !== "/dashboard";
//   };

//   // Dummy data for sidebar menu
//   const sidebarMenu = [
//     { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
//     // قائمة Menu مع state منفصل
//     {
//       label: "Menu",
//       icon: UtensilsCrossed,
//       isSubmenu: true,
//       open: menuOpen,
//       setOpen: setMenuOpen,
//       activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
//       submenu: [
//         { href: "/menus", label: "Menus", icon: "📋"},
//         { href: "/menu-items", label: "Menu Items", icon: "🍽️"},
//         { href: "/item-categories", label: "Item Categories", icon: "📂"},
//         { href: "/modefiers", label: "Modifier Groups", icon: "🔧" },
//         { href: "/itemModefiers", label: "Item Modifiers", icon: "⚙️"},
//         { href: "/itemMenue", label: "itemMenue", icon: "📝"},
//       ],
//     },
//     // قائمة Tables مع state منفصل
//     {
//       label: "tabels",
//       icon: HandPlatter,
//       isSubmenu: true,
//       open: tablesOpen,
//       setOpen: setTablesOpen,
//       activePaths: ["/areas", "/tabels", "/qrcodes"],
//       submenu: [
//         { href: "/areas", label: "Areas", icon: "🏢"},
//         { href: "/tabels", label: "Tabels", icon: "🪑"},
//         { href: "/qrcodes", label: "Qr codes", icon: "📱"},
//       ],
//     },
//     { href: "/staff", icon: UserCheck, label: "Staff" },
//     { href: "/customers", icon: Users, label: "Customers" },
//     { href: "/pos", icon: ShoppingCart, label: "pos" },
//     {
//       label: t.sidebar?.orders || "Orders",
//       icon: ClipboardList,
//       isSubmenu: true,
//       open: ordersOpen,
//       setOpen: setOrdersOpen,
//       activePaths: ["/OrdersT", "/kot"],
//       submenu: [
//         { href: "/kot", label: t.sidebar?.kot || "kot", icon: "🧾" },
//         { href: "/OrdersT", label: t.sidebar?.orders || "orders", icon: "📦" },
//       ],
//     },
//     {
//       href: "/diliveryExcutive",
//       icon: Truck,
//       label: "delivery",
//     },

//     { href: "/Settings", icon: Cog, label: "Settings" },

//     // {
//     //   label: t.sidebar?.settings || "Settings",
//     //   icon: Settings,
//     //   isSubmenu: true,
//     //   open: settingsOpen,
//     //   setOpen: setSettingsOpen,
//     //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
//     //   submenu: [
//     //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
//     //     {
//     //       href: "/settings/preferences",
//     //       label: t.sidebar?.preferences || "Preferences",
//     //       icon: "⚙️",
//     //     },
//     //     {
//     //       href: "/settings/security",
//     //       label: t.sidebar?.security || "Security",
//     //       icon: "🔒",
//     //     },
//     //   ],
//     // },
//   ];

//   // useEffect(() => {
//   //   if (isOpen) {
//   //     toggleSidebar(); // Close on route change
//   //   }
//   // }, [pathname]);

//   // useEffect(() => {
//   //   if (
//   //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
//   //       pathname.startsWith(p)
//   //     )
//   //   ) {
//   //     setSettingsOpen(false);
//   //   }
//   // }, [pathname]);

//   return (
//     <>
//       {/* Overlay for mobile */}
//       <div
//         className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
//           isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
//         }`}
//         onClick={toggleSidebar}
//       />

//       {/* Toggle Button */}
//       {/* <button
//         onClick={toggleSidebar}
//         className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
//         aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
//         type="button"
//       >
//         {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
//       </button> */}

//       {/* Sidebar content */}
//       <div
//         className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
//           isOpen ? "left-0" : "-left-64"
//         }`}
//       >
//         {/* Brand */}
        
//         {/* Branch Selector */}
//         <div className="px-4 pt-6 pb-2">
//           <div
//             className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
//             onClick={() => setBranchDropdownOpen((open) => !open)}
//           >
//             <span className="flex items-center gap-2">
//               <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
//                 <circle cx="12" cy="10" r="3" />
//                 <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
//               </svg>
//               <span className="font-bold text-primary">{selectedBranch.name}</span>
//             </span>
//             <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
//               <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
//             </svg>
//             {branchDropdownOpen && (
//               <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
//                 {branches.map((branch) => (
//                   <div
//                     key={branch.id}
//                     className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
//                     style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
//                     onClick={() => {
//                       setSelectedBranch(branch);
//                       setBranchDropdownOpen(false);
//                     }}
//                   >
//                     {branch.name}
//                   </div>
//                 ))}
//               </div>
//             )}
//           </div>
//         </div>

//         {/* Menu */}
//         <div className="flex-1 overflow-y-auto  pt-5">
//           {sidebarMenu.map((item, idx) => {
//             if (!item.isSubmenu) {
//               const Icon = item.icon;
//               return (
//                 <Link
//                   key={item.href}
//                   href={item.href}
//                   className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
//                     isLinkActive(item.href)
//                       ? "bg-primary text-white"
//                       : "text-gray-700 hover:bg-gray-100"
//                   }`}
//                 >
//                   <Icon className="h-5 w-5 mr-3" />
//                   <span>{item.label}</span>
//                 </Link>
//               );
//             }
//             // Submenu
//             const Icon = item.icon;
//             return (
//               <div className="relative" key={item.label}>
//                 <button
//                   type="button"
//                   onClick={() => item.setOpen((prev) => !prev)}
//                   aria-expanded={item.open}
//                   aria-controls={`${item.label}-submenu`}
//                   className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-2 ${
//                     item.open || item.activePaths.some((p) => pathname.startsWith(p))
//                       ? "border-primary bg-transparent text-primary"
//                       : "border-transparent text-gray-700 hover:bg-gray-100"
//                   }`}
//                 >
//                   <Icon className="h-5 w-5 mr-3" />
//                   <span>{item.label}</span>
//                   <svg
//                     className={`ml-auto h-4 w-4 transition-transform duration-200 ${
//                       item.open ? "rotate-90" : "rotate-0"
//                     }`}
//                     fill="none"
//                     stroke="currentColor"
//                     viewBox="0 0 24 24"
//                   >
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M9 5l7 7-7 7"
//                     />
//                   </svg>
//                 </button>
//                 <div
//                   id={`${item.label}-submenu`}
//                   className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ${
//                     item.open ? "max-h-60 py-2" : "max-h-0 py-0"
//                   }`}
//                   style={{
//                     boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
//                   }}
//                 >
//                   {item.submenu.map(({ href, label, icon }) => (
//                     <Link
//                       key={href}
//                       href={href}
//                       className={`flex items-center px-4 py-2 transition-colors rounded-md ${
//                         isLinkActive(href)
//                           ? "bg-primary text-white"
//                           : "text-gray-700 hover:bg-gray-100"
//                       }`}
//                     >
//                       <span className="mr-2">{icon}</span>
//                       <span>{label}</span>
//                     </Link>
//                   ))}
//                 </div>
//               </div>
//             );
//           })}
//         </div>
//       </div>
//     </>
//   );
// };

// export default Sidebar;
import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  BarChart2,
  Home,
  AlertTriangle,
  Users2,
  Calendar,
  FolderKanban,
  Settings,
  X,
  Menu,
  MessageCircle,
  UtensilsCrossed,
  Grid3x3,
  Layers,
  Sliders,
  PlusCircle,
  MapPin,
  QrCode,
  UserCheck,
  Users,
  ShoppingCart,
  ClipboardList,
  ChefHat,
  Truck,
  Cog,
  Table,
  HandPlatter,
  Package,
  FileText,
  BarChart3,
  TrendingUp
} from "lucide-react";

import { useSidebar } from "@/context/SidebarContext";
import { useI18n } from "@/context/translate-api";

const Sidebar = () => {
  const { isOpen, toggleSidebar } = useSidebar();
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const pathname = usePathname();
  const { t } = useI18n();
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [ordersOpen, setOrdersOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false); // للقائمة الأولى Menu
  const [tablesOpen, setTablesOpen] = useState(false); // للقائمة الثانية Tables
  const [reportsOpen, setReportsOpen] = useState(false); // للقائمة Reports
  const [branches] = useState([
    { id: 1, name: "DEGLA" },
    { id: 2, name: "MAADI" },
    // Add more branches as needed
  ]);
  const [selectedBranch, setSelectedBranch] = useState(branches[0]);
  const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);

  const cancelLogout = () => setShowLogoutConfirm(false);

  const isLinkActive = (path) => {
    if (path === "/dashboard" && pathname === "/dashboard") return true;
    return pathname.startsWith(path) && path !== "/dashboard";
  };

  // Dummy data for sidebar menu
  const sidebarMenu = [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    // قائمة Menu مع state منفصل
    {
      label: "Menu",
      icon: UtensilsCrossed,
      isSubmenu: true,
      open: menuOpen,
      setOpen: setMenuOpen,
      activePaths: ["/menus", "/menu-items", "/item-categories", "/modefiers", "/itemModefiers","itemMenue"],
      submenu: [
        { href: "/menus", label: "Menus"},
        { href: "/menu-items", label: "Menu Items"},
        { href: "/item-categories", label: "Item Categories"},
        { href: "/modefiers", label: "Modifier Groups"},
        { href: "/itemModefiers", label: "Item Modifiers"},
        { href: "/itemMenue", label: "itemMenue"},
      ],
    },
    // قائمة Tables مع state منفصل
    {
      label: "tabels",
      icon: HandPlatter,
      isSubmenu: true,
      open: tablesOpen,
      setOpen: setTablesOpen,
      activePaths: ["/areas", "/tabels", "/qrcodes"],
      submenu: [
        { href: "/areas", label: "Areas"},
        { href: "/tabels", label: "Tabels"},
        { href: "/qrcodes", label: "Qr codes"},
      ],
    },
    { href: "/staff", icon: UserCheck, label: "Staff" },
    { href: "/customers", icon: Users, label: "Customers" },
    { href: "/inventory", icon: Package, label: "Inventory" },
    { href: "/pos", icon: ShoppingCart, label: "pos" },
    {
      label: t.sidebar?.orders || "Orders",
      icon: ClipboardList,
      isSubmenu: true,
      open: ordersOpen,
      setOpen: setOrdersOpen,
      activePaths: ["/OrdersT", "/kot"],
      submenu: [
        { href: "/kot", label: t.sidebar?.kot || "kot"},
        { href: "/OrdersT", label: t.sidebar?.orders || "orders" },
      ],
    },
    {
      href: "/diliveryExcutive",
      icon: Truck,
      label: "delivery",
    },

    {
      label: "Reports",
      icon: FileText,
      isSubmenu: true,
      open: reportsOpen,
      setOpen: setReportsOpen,
      activePaths: ["/reports/sales", "/reports/analytics"],
      submenu: [
        { href: "/reports/sales", label: "Sales Report", icon: "📊" },
        { href: "/reports/analytics", label: "Analytics", icon: "📈" },
      ],
    },

    { href: "/Settings", icon: Cog, label: "Settings" },

    // {
    //   label: t.sidebar?.settings || "Settings",
    //   icon: Settings,
    //   isSubmenu: true,
    //   open: settingsOpen,
    //   setOpen: setSettingsOpen,
    //   activePaths: ["/pos", "/settings/preferences", "/settings/security"],
    //   submenu: [
    //     { href: "/pos", label: t.sidebar?.profile || "Profile", icon: "👤" },
    //     {
    //       href: "/settings/preferences",
    //       label: t.sidebar?.preferences || "Preferences",
    //       icon: "⚙️",
    //     },
    //     {
    //       href: "/settings/security",
    //       label: t.sidebar?.security || "Security",
    //       icon: "🔒",
    //     },
    //   ],
    // },
  ];

  // useEffect(() => {
  //   if (isOpen) {
  //     toggleSidebar(); // Close on route change
  //   }
  // }, [pathname]);

  // useEffect(() => {
  //   if (
  //     !["/pos", "/settings/preferences", "/settings/security"].some((p) =>
  //       pathname.startsWith(p)
  //     )
  //   ) {
  //     setSettingsOpen(false);
  //   }
  // }, [pathname]);

  return (
    <>
      {/* Overlay for mobile */}
      <div
        className={`lg:hidden fixed inset-0  bg-black/60 z-10 transition-opacity duration-300  ${
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={toggleSidebar}
      />

      {/* Toggle Button */}
      {/* <button
        onClick={toggleSidebar}
        className="fixed top-20 left-4 z-50 p-2 rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors text-gray-700 shadow-lg"
        aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
        type="button"
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </button> */}

      {/* Sidebar content */}
      <div
        className={`w-64 h-full bg-white text-gray-700 flex flex-col fixed top-20   z-40 transition-all duration-300 shadow-md ${
          isOpen ? "left-0" : "-left-64"
        }`}
      >
        {/* Brand */}
        
        {/* Branch Selector */}
        <div className="px-4 pt-6 pb-2">
          <div
            className="flex items-center justify-between border-2 border-primary rounded-xl px-4 py-2 cursor-pointer relative"
            onClick={() => setBranchDropdownOpen((open) => !open)}
          >
            <span className="flex items-center gap-2">
              <svg width="20" height="20" fill="none" stroke="#1e2a78" strokeWidth="2" viewBox="0 0 24 24">
                <circle cx="12" cy="10" r="3" />
                <path d="M12 2v2m0 12v2m8-8h-2M6 12H4m15.07 7.07l-1.41-1.41M6.34 6.34l-1.41-1.41" />
              </svg>
              <span className="font-bold text-primary">{selectedBranch.name}</span>
            </span>
            <svg className="ml-2 w-5 h-5 text-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
            {branchDropdownOpen && (
              <div className="absolute left-0 right-0 top-full mt-2 bg-white border border-primary rounded-xl shadow-lg z-50">
                {branches.map((branch) => (
                  <div
                    key={branch.id}
                    className={`px-4 py-2 cursor-pointer rounded-xl transition-colors ${branch.id === selectedBranch.id ? "bg-primary text-white" : "text-primary"}`}
                    style={{ backgroundColor: branch.id === selectedBranch.id ? undefined : "transparent" }}
                    onClick={() => {
                      setSelectedBranch(branch);
                      setBranchDropdownOpen(false);
                    }}
                  >
                    {branch.name}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Menu */}
        <div className="flex-1 overflow-y-auto  pt-5">
          {sidebarMenu.map((item, idx) => {
            if (!item.isSubmenu) {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center px-4 py-2 mb-1 gap-2 transition-colors ${
                    isLinkActive(item.href)
                      ? "bg-primary text-white"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  <span>{item.label}</span>
                </Link>
              );
            }
            // Submenu
            const Icon = item.icon;
            return (
              <div className="relative" key={item.label}>
                <button
                  type="button"
                  onClick={() => item.setOpen((prev) => !prev)}
                  aria-expanded={item.open}
                  aria-controls={`${item.label}-submenu`}
                  className={`flex items-center w-full px-4 py-2 mb-1 gap-2 transition-colors rounded-md border-l-2 ${
                    item.open || item.activePaths.some((p) => pathname.startsWith(p))
                      ? "border-primary bg-transparent text-primary"
                      : "border-transparent text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  <span>{item.label}</span>
                  <svg
                    className={`ml-auto h-4 w-4 transition-transform duration-200 ${
                      item.open ? "rotate-90" : "rotate-0"
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
                <div
                  id={`${item.label}-submenu`}
                  className={`overflow-hidden transition-all duration-300 bg-gray-50 rounded-md ps-6 ${
                    item.open ? "max-h-60 py-2" : "max-h-0 py-0"
                  }`}
                  style={{
                    boxShadow: item.open ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
                  }}
                >
                  {item.submenu.map(({ href, label, icon }) => (
                    <Link
                      key={href}
                      href={href}
                      className={`flex items-center px-4 py-2 transition-colors rounded-md ${
                        isLinkActive(href)
                          ? "bg-primary text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      <span className="mr-2">{icon}</span>
                      <span>{label}</span>
                    </Link>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default Sidebar;