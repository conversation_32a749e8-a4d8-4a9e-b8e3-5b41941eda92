"use client"
import React from 'react'
import { Edit, Plus, Trash2 } from 'lucide-react'

const ItemModifiersGrid = () => {
  const modifiers = [
    {
      id: 1,
      title: "Sauces",
      modifierName: "Ketchup",
      isRequired: false,
      allowMultipleSelection: false
    },
    {
      id: 2,
      title: "Sauces",
      modifierName: "Mayonnaise",
      isRequired: false,
      allowMultipleSelection: false
    },
    {
      id: 3,
      title: "Extras",
      modifierName: "Cheese",
      isRequired: true,
      allowMultipleSelection: true
    },
    {
      id: 4,
      title: "Extras",
      modifierName: "Bacon",
      isRequired: true,
      allowMultipleSelection: true
    }
  ]

  return (
    <div className="p-8 bg-gray-50 ">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          Item Modifiers Management
        </h1>
         <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="flex justify-end p-4 pb-0">
            <button
              onClick={() => alert('Add new modifier')}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
              إضافة معدل جديد
            </button>
          </div>
          <div className="overflow-x-auto mt-2">
            <table className="w-full">
              <thead>
                <tr className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                  <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
                    Modifier Name
                  </th>
                  <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
                    Multiple Selection
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-semibold uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {modifiers.map((modifier, index) => (
                  <tr 
                    key={modifier.id}
                    className={`${
                      index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                    } hover:bg-orange-50 transition-colors duration-200`}
                  >
                    <td className=" py-4 mx-auto whitespace-nowrap ">
                      <div className=" text-center w-6 h-6 flex items-center justify-center mx-auto px-2   bg-orange-100 rounded-full">
                        <span className="text-sm font-medium text-orange-600">
                          {modifier.id}
                        </span>
                      </div>
                    </td>
                    <td className=" py-4 whitespace-nowrap">
                      <div className="flex  text-center justify-center  items-center">
                        <div className={` rounded-full  mr-3 ${
                          modifier.title === 'Sauces' ? 'bg-blue-400' : 'bg-green-400'
                        }`}></div>
                        <span className="text-sm font-medium text-gray-900">
                          {modifier.title}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-sm font-semibold text-gray-800">
                        {modifier.modifierName}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap  text-center">
                      <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${
                        modifier.isRequired 
                          ? 'bg-red-100 text-red-800 border border-red-200' 
                          : 'bg-green-100 text-green-800 border border-green-200'
                      }`}>
                        {modifier.isRequired ? 'Required' : 'Optional'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap  ">
                      <div className="flex items-center  justify-center text-center ">
                      
                        <span className={`text-sm font-medium ${
                          modifier.allowMultipleSelection ? 'text-orange-600' : 'text-gray-500'
                        }`}>
                          {modifier.allowMultipleSelection ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => console.log('Edit modifier', modifier.id)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                          title="Edit Modifier"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => console.log('Delete modifier', modifier.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
                          title="Delete Modifier"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
        
        </div>
      </div>
    </div>
  )
}

export default ItemModifiersGrid