import React, { Suspense } from 'react'
import MenueFiltetr from './_component/MenueFiltetr'
import MenueGridServer from './_component/MenueGridServer'
import { fetchCategories } from '@/component/sevices/serviceFeatching'
import { Loader2 } from 'lucide-react'
import { redirect } from 'next/navigation'

const page = async ({ searchParams: rawSearchParams }) => {
  const searchParams = await rawSearchParams
  const categories = await fetchCategories()

  if (!searchParams.category && categories && categories.length > 0) {
    redirect(`/orders?category=${categories[0]}`)
  }

  return (
    <div>
      <MenueFiltetr categories={categories} applayfilter={searchParams} />
     
       <Suspense
          key={JSON.stringify(searchParams.category)}
          fallback={
            <div className="flex items-center justify-center h-full mt-12">
              <Loader2
                size={70}
                className="text-center animate-spin text-primary"
              />
            </div>
          }
        >
          <MenueGridServer category={searchParams.category} />
        </Suspense>
    </div>
  )
}

export default page
