// "use client";
// import React, { useState, useEffect } from "react";
// import { Edit, Trash2, Plus, Search, Filter, MoreVertical, X, Eye, ChevronRight } from "lucide-react";

// const ModifiersGrid = () => {
//   const [searchTerm, setSearchTerm] = useState("");
//   const [selectedCategory, setSelectedCategory] = useState("all");
//   const [showDetailsModal, setShowDetailsModal] = useState(false);
//   const [selectedCategoryDetails, setSelectedCategoryDetails] = useState(null);
//   const [showAddModal, setShowAddModal] = useState(false);
//   const [showEditModal, setShowEditModal] = useState(false);
//   const [showDeleteModal, setShowDeleteModal] = useState(false);
//   const [currentCategory, setCurrentCategory] = useState(null);

//   const [data, setData] = useState([
//     {
//       id: 1,
//       title: "Beverages",
//       color: "from-purple-500 to-pink-500",
//       bgColor: "bg-purple-50",
//       textColor: "text-purple-700",
//       borderColor: "border-purple-200",
//       opt: [
//         { objectName: "Coffee", price: 100 },
//         { objectName: "Tea", price: 150 },
//         { objectName: "Juice", price: 450 },
//       ],
//     },
//     {
//       id: 2,
//       title: "Main Dishes",
//       color: "from-blue-500 to-cyan-500",
//       bgColor: "bg-blue-50",
//       textColor: "text-blue-700",
//       borderColor: "border-blue-200",
//       opt: [
//         { objectName: "Pasta", price: 200 },
//         { objectName: "Pizza", price: 250 },
//         { objectName: "Burger", price: 300 },
//         { objectName: "Steak", price: 400 },
//         { objectName: "Salad", price: 180 },
//         { objectName: "Sandwich", price: 220 },
//         { objectName: "Soup", price: 150 },
//         { objectName: "Rice Bowl", price: 190 },
//         { objectName: "Noodles", price: 210 },
//         { objectName: "Grilled Chicken", price: 350 },
//       ],
//     },
//     {
//       id: 3,
//       title: "Desserts",
//       color: "from-emerald-500 to-teal-500",
//       bgColor: "bg-emerald-50",
//       textColor: "text-emerald-700",
//       borderColor: "border-emerald-200",
//       opt: [
//         { objectName: "Ice Cream", price: 300 },
//         { objectName: "Cake", price: 350 },
//       ],
//     },
//     {
//       id: 4,
//       title: "Appetizers",
//       color: "from-orange-500 to-red-500",
//       bgColor: "bg-orange-50",
//       textColor: "text-orange-700",
//       borderColor: "border-orange-200",
//       opt: [
//         { objectName: "Wings", price: 180 },
//       ],
//     },
//   ]);

//   const [filteredData, setFilteredData] = useState(data);

//   useEffect(() => {
//     let filtered = data
//       .filter(
//         (category) =>
//           selectedCategory === "all" ||
//           category.id.toString() === selectedCategory
//       )
//       .filter((category) =>
//         category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         category.opt.some((option) =>
//           option.objectName.toLowerCase().includes(searchTerm.toLowerCase())
//         )
//       );
//     setFilteredData(filtered);
//   }, [data, searchTerm, selectedCategory]);

//   const openDetailsModal = (category) => {
//     setSelectedCategoryDetails(category);
//     setShowDetailsModal(true);
//   };

//   const handleEdit = (category) => {
//     setCurrentCategory(category);
//     setShowEditModal(true);
//   };

//   const handleDelete = (category) => {
//     setCurrentCategory(category);
//     setShowDeleteModal(true);
//   };

//   const confirmDelete = () => {
//     if (currentCategory) {
//       setData(data.filter(cat => cat.id !== currentCategory.id));
//       setShowDeleteModal(false);
//       setCurrentCategory(null);
//     }
//   };

//   // Modal component
//   const Modal = ({ isOpen, onClose, title, children, size = "max-w-2xl" }) => {
//     if (!isOpen) return null;

//     return (
//       <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//         <div className={`bg-white rounded-2xl shadow-2xl w-full ${size} max-h-[90vh] flex flex-col`}>
//           <div className="flex justify-between items-center p-6 border-b border-gray-200">
//             <h3 className="text-2xl font-bold text-gray-800">{title}</h3>
//             <button
//               onClick={onClose}
//               className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
//             >
//               <X size={24} />
//             </button>
//           </div>
//           <div className="flex-1 overflow-y-auto">
//             {children}
//           </div>
//         </div>
//       </div>
//     );
//   };

//   return (
//     <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
//       <div className="max-w-7xl mx-auto">
//         {/* Header Section */}
//         <div className="mb-8">
//           <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
//             <div>
//               <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
//                 Modifiers Management
//               </h1>
//               <p className="text-gray-600 text-lg">Manage categories and their items efficiently</p>
//             </div>
//             <button 
//               onClick={() => setShowAddModal(true)}
//               className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
//             >
//               <Plus className="w-5 h-5" />
//               Add New Category
//             </button>
//           </div>
//         </div>

//         {/* Search and Filter Section */}
//         <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 mb-8">
//           <div className="flex flex-col md:flex-row gap-4">
//             <div className="relative flex-1">
//               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <input
//                 type="text"
//                 placeholder="Search categories or items..."
//                 value={searchTerm}
//                 onChange={(e) => setSearchTerm(e.target.value)}
//                 className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//               />
//             </div>
//             <div className="relative">
//               <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <select
//                 value={selectedCategory}
//                 onChange={(e) => setSelectedCategory(e.target.value)}
//                 className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
//               >
//                 <option value="all">All Categories</option>
//                 {data.map((category) => (
//                   <option key={category.id} value={category.id.toString()}>
//                     {category.title}
//                   </option>
//                 ))}
//               </select>
//             </div>
//           </div>
//         </div>

//         {/* Data Grid */}
//         <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
//           <div className="overflow-x-auto">
//             <table className="w-full">
//               <thead>
//                 <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
//                   <th className="px-6 py-4  text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     ID
//                   </th>
//                   <th className="px-6 py-4  text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Category
//                   </th>
//                   <th className="px-6 py-4  text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Items Count
//                   </th>
//                   <th className="px-6 py-4  text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Sample Item
//                   </th>
//                   <th className="px-6 py-4  text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Price Range
//                   </th>
//                   <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="divide-y divide-gray-200">
//                 {filteredData.length > 0 ? (
//                   filteredData.map((category, index) => {
//                     const minPrice = Math.min(...category.opt.map(item => item.price));
//                     const maxPrice = Math.max(...category.opt.map(item => item.price));
                    
//                     return (
//                       <tr key={category.id} className="hover:bg-gray-50 transition-all duration-200">
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <span className="text-sm font-medium text-gray-900">
//                             #{category.id}
//                           </span>
//                         </td>
//                         <td className="px-6  flex items-center justify-center py-4 whitespace-nowrap  ">
//                           <div className=" gap-3  flex items-center justify-center">
//                             <div className={`w-3 h-3 rounded-full text-center  bg-gradient-to-r ${category.color}`}></div>
//                             <span className="text-sm font-semibold text-gray-900">
//                               {category.title}
//                             </span>
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-center">
//                           <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full  text-center text-xs font-medium ${category.bgColor} ${category.textColor}`}>
//                             {category.opt.length} item{category.opt.length !== 1 ? 's' : ''}
//                           </span>
//                         </td>
//                         <td className="px-6 py-4 text-center">
//                           <div className="flex items-center justify-center">
//                             <div>
//                               <div className="text-sm font-medium text-gray-900">
//                                 {category.opt[0]?.objectName}
//                               </div>
                            
//                             </div>
//                             {category.opt.length > 1 && (
//                               <button
//                                 onClick={() => openDetailsModal(category)}
//                                 className="flex items-center gap-1 px-3 py-1 text-xs text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 font-medium"
//                               >
//                                 +{category.opt.length - 1} more
//                                 <ChevronRight className="w-3 h-3" />
//                               </button>
//                             )}
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-center">
//                           <div className="text-sm text-gray-900">
//                             ${minPrice.toFixed(2)} - ${maxPrice.toFixed(2)}
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-center">
//                           <div className="flex items-center justify-center gap-2">
//                             <button
//                               onClick={() => openDetailsModal(category)}
//                               className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="View Details"
//                             >
//                               <Eye className="w-4 h-4" />
//                             </button>
//                             <button
//                               onClick={() => handleEdit(category)}
//                               className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="Edit"
//                             >
//                               <Edit className="w-4 h-4" />
//                             </button>
//                             <button
//                               onClick={() => handleDelete(category)}
//                               className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="Delete"
//                             >
//                               <Trash2 className="w-4 h-4" />
//                             </button>
//                           </div>
//                         </td>
//                       </tr>
//                     );
//                   })
//                 ) : (
//                   <tr>
//                     <td colSpan="6" className="px-6 py-12 text-center">
//                       <div className="flex flex-col items-center justify-center">
//                         <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
//                           <Search className="w-8 h-8 text-gray-400" />
//                         </div>
//                         <h3 className="text-lg font-semibold text-gray-900 mb-2">
//                           No Results Found
//                         </h3>
//                         <p className="text-gray-500">
//                           Try adjusting your search terms or filters
//                         </p>
//                       </div>
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//           </div>
//         </div>

//         {/* Results Count */}
//         <div className="mt-6 text-center">
//           <span className="text-sm text-gray-600">
//             Showing {filteredData.length} of {data.length} categories
//           </span>
//         </div>

//         {/* Details Modal */}
//         <Modal
//           isOpen={showDetailsModal}
//           onClose={() => setShowDetailsModal(false)}
//           title={`${selectedCategoryDetails?.title} - All Items`}
//           size="max-w-4xl"
//         >
//           {selectedCategoryDetails && (
//             <div className="p-6">
//               <div className="mb-6">
//                 <div className="flex items-center gap-3 mb-4">
//                   <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${selectedCategoryDetails.color}`}></div>
//                   <h4 className="text-xl font-bold text-gray-800">{selectedCategoryDetails.title}</h4>
//                   <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedCategoryDetails.bgColor} ${selectedCategoryDetails.textColor}`}>
//                     {selectedCategoryDetails.opt.length} items
//                   </span>
//                 </div>
//               </div>
              
//               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
//                 {selectedCategoryDetails.opt.map((option, index) => (
//                   <div
//                     key={`${index}-${option.objectName}`}
//                     className="bg-gradient-to-br from-gray-50 to-white p-4 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200"
//                   >
//                     <div className="flex items-center justify-between">
//                       <div className="flex-1">
//                         <h5 className="font-semibold text-gray-900 mb-2">
//                           {option.objectName}
//                         </h5>
//                         <div className="text-xl font-bold text-[#FF6500]">
//                           ${option.price?.toFixed(2)}
//                         </div>
//                       </div>
//                       <div className="flex gap-1">
//                         <button className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200">
//                           <Edit className="w-4 h-4" />
//                         </button>
//                         <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200">
//                           <Trash2 className="w-4 h-4" />
//                         </button>
//                       </div>
//                     </div>
//                   </div>
//                 ))}
//               </div>
              
//               <div className="mt-6 flex justify-end">
//                 <button
//                   onClick={() => setShowDetailsModal(false)}
//                   className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
//                 >
//                   Close
//                 </button>
//               </div>
//             </div>
//           )}
//         </Modal>

//         {/* Add Modal */}
//         <Modal
//           isOpen={showAddModal}
//           onClose={() => setShowAddModal(false)}
//           title="Add New Category"
//         >
//           <div className="p-6">
//             <div className="space-y-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Category Name
//                 </label>
//                 <input
//                   type="text"
//                   placeholder="Enter category name"
//                   className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                 />
//               </div>
//               <div className="flex gap-3 justify-end pt-4">
//                 <button
//                   onClick={() => setShowAddModal(false)}
//                   className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
//                 >
//                   Cancel
//                 </button>
//                 <button className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
//                   Add Category
//                 </button>
//               </div>
//             </div>
//           </div>
//         </Modal>

//         {/* Delete Confirmation Modal */}
//         <Modal
//           isOpen={showDeleteModal}
//           onClose={() => setShowDeleteModal(false)}
//           title="Delete Category"
//           size="max-w-lg"
//         >
//           <div className="p-6">
//             <div className="text-center">
//               <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
//                 <Trash2 className="w-8 h-8 text-red-600" />
//               </div>
//               <h3 className="text-lg font-semibold text-gray-900 mb-2">
//                 Are you sure?
//               </h3>
//               <p className="text-gray-600 mb-6">
//                 This will permanently delete the category "{currentCategory?.title}" and all its items. This action cannot be undone.
//               </p>
//               <div className="flex gap-3 justify-center">
//                 <button
//                   onClick={() => setShowDeleteModal(false)}
//                   className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={confirmDelete}
//                   className="px-6 py-3 bg-red-600 text-white font-semibold rounded-xl hover:bg-red-700 transition-all duration-200"
//                 >
//                   Delete Category
//                 </button>
//               </div>
//             </div>
//           </div>
//         </Modal>
//       </div>
//     </div>
//   );
// };

// export default ModifiersGrid;
"use client";
import React, { useState, useEffect } from "react";
import { Edit, Trash2, Plus, Search, Filter, MoreVertical, X, Eye, ChevronRight, Loader2 } from "lucide-react";
import { fetchCategories } from '../../../../../lib/api';

const ModifiersGrid = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedCategoryDetails, setSelectedCategoryDetails] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);

  // Color schemes for categories
  const colorSchemes = [
    {
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      borderColor: "border-purple-200",
    },
    {
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      borderColor: "border-blue-200",
    },
    {
      color: "from-emerald-500 to-teal-500",
      bgColor: "bg-emerald-50",
      textColor: "text-emerald-700",
      borderColor: "border-emerald-200",
    },
    {
      color: "from-orange-500 to-red-500",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
      borderColor: "border-orange-200",
    },
    {
      color: "from-indigo-500 to-purple-500",
      bgColor: "bg-indigo-50",
      textColor: "text-indigo-700",
      borderColor: "border-indigo-200",
    },
    {
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      borderColor: "border-green-200",
    }
  ];

  // Transform API data to match existing component structure
  const transformApiData = async (categories) => {
    const transformedData = [];
    
    for (let i = 0; i < categories.length; i++) {
      const category = categories[i];
      const colorScheme = colorSchemes[i % colorSchemes.length];
      
      try {
        // Fetch menu items for this category (you might need to adjust this based on your API)
        // For now, I'll create sample items since we don't have the exact menu items API structure
        const menuItems = [
          { objectName: `${category.name} Item 1`, price: Math.floor(Math.random() * 200) + 100 },
          { objectName: `${category.name} Item 2`, price: Math.floor(Math.random() * 200) + 100 },
        ];

        transformedData.push({
          id: category.id,
          title: category.name,
          description: category.description,
          code: category.code,
          image_url: category.image_url,
          sort_order: category.sort_order,
          is_active: category.is_active,
          menu_id: category.menu_id,
          ...colorScheme,
          opt: menuItems
        });
      } catch (error) {
        console.error(`Error fetching items for category ${category.name}:`, error);
        // Add category without items if API call fails
        transformedData.push({
          id: category.id,
          title: category.name,
          description: category.description,
          code: category.code,
          image_url: category.image_url,
          sort_order: category.sort_order,
          is_active: category.is_active,
          menu_id: category.menu_id,
          ...colorScheme,
          opt: []
        });
      }
    }
    
    return transformedData.sort((a, b) => a.sort_order - b.sort_order);
  };

  // Fetch data from API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        const categories = await fetchCategories();
        const transformedData = await transformApiData(categories);
        setData(transformedData);
      } catch (err) {
        setError('Failed to load categories');
        console.error('Error loading categories:', err);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Filter data based on search and category selection
  useEffect(() => {
    let filtered = data
      .filter(
        (category) =>
          selectedCategory === "all" ||
          category.id.toString() === selectedCategory
      )
      .filter((category) =>
        category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.opt.some((option) =>
          option.objectName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    setFilteredData(filtered);
  }, [data, searchTerm, selectedCategory]);

  const openDetailsModal = (category) => {
    setSelectedCategoryDetails(category);
    setShowDetailsModal(true);
  };

  const handleEdit = (category) => {
    setCurrentCategory(category);
    setShowEditModal(true);
  };

  const handleDelete = (category) => {
    setCurrentCategory(category);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (currentCategory) {
      setData(data.filter(cat => cat.id !== currentCategory.id));
      setShowDeleteModal(false);
      setCurrentCategory(null);
    }
  };

  // Modal component
  const Modal = ({ isOpen, onClose, title, children, size = "max-w-2xl" }) => {
    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className={`bg-white rounded-2xl shadow-2xl w-full ${size} max-h-[90vh] flex flex-col`}>
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-2xl font-bold text-gray-800">{title}</h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
            >
              <X size={24} />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-3">
              <Loader2 className="w-8 h-8 animate-spin text-[#FF6500]" />
              <span className="text-lg font-medium text-gray-600">Loading categories...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Categories</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
                Modifiers Management
              </h1>
              <p className="text-gray-600 text-lg">Manage categories and their items efficiently</p>
            </div>
            <button 
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
            >
              <Plus className="w-5 h-5" />
              Add New Category
            </button>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search categories or items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
              >
                <option value="all">All Categories</option>
                {data.map((category) => (
                  <option key={category.id} value={category.id.toString()}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Data Grid */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Items Count
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Sort Order
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredData.length > 0 ? (
                  filteredData.map((category, index) => {
                    return (
                      <tr key={category.id} className="hover:bg-gray-50 transition-all duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            #{category.id}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${category.color}`}></div>
                            <div>
                              <div className="text-sm font-semibold text-gray-900">
                                {category.title}
                              </div>
                              {category.description && (
                                <div className="text-xs text-gray-500 max-w-xs truncate">
                                  {category.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-600 font-mono">
                            {category.code}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${category.bgColor} ${category.textColor}`}>
                            {category.opt.length} item{category.opt.length !== 1 ? 's' : ''}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            category.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {category.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            {category.sort_order}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-2">
                            <button
                              onClick={() => openDetailsModal(category)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="View Details"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEdit(category)}
                              className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(category)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Delete"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <Search className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          No Results Found
                        </h3>
                        <p className="text-gray-500">
                          Try adjusting your search terms or filters
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-6 text-center">
          <span className="text-sm text-gray-600">
            Showing {filteredData.length} of {data.length} categories
          </span>
        </div>

        {/* Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title={`${selectedCategoryDetails?.title} - Details`}
          size="max-w-4xl"
        >
          {selectedCategoryDetails && (
            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${selectedCategoryDetails.color}`}></div>
                  <h4 className="text-xl font-bold text-gray-800">{selectedCategoryDetails.title}</h4>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedCategoryDetails.bgColor} ${selectedCategoryDetails.textColor}`}>
                    {selectedCategoryDetails.opt.length} items
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Code</p>
                    <p className="font-mono text-sm">{selectedCategoryDetails.code}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Sort Order</p>
                    <p className="text-sm">{selectedCategoryDetails.sort_order}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Menu ID</p>
                    <p className="text-sm">{selectedCategoryDetails.menu_id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedCategoryDetails.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {selectedCategoryDetails.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                {selectedCategoryDetails.description && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500">Description</p>
                    <p className="text-sm text-gray-900">{selectedCategoryDetails.description}</p>
                  </div>
                )}
              </div>
              
              {selectedCategoryDetails.opt.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {selectedCategoryDetails.opt.map((option, index) => (
                    <div
                      key={`${index}-${option.objectName}`}
                      className="bg-gradient-to-br from-gray-50 to-white p-4 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h5 className="font-semibold text-gray-900 mb-2">
                            {option.objectName}
                          </h5>
                          <div className="text-xl font-bold text-[#FF6500]">
                            ${option.price?.toFixed(2)}
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <button className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>

        {/* Add Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Category"
        >
          <div className="p-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category Name
                </label>
                <input
                  type="text"
                  placeholder="Enter category name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  placeholder="Enter category description"
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div className="flex gap-3 justify-end pt-4">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
                >
                  Cancel
                </button>
                <button className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
                  Add Category
                </button>
              </div>
            </div>
          </div>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Delete Category"
          size="max-w-lg"
        >
          <div className="p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trash2 className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Are you sure?
              </h3>
              <p className="text-gray-600 mb-6">
                This will permanently delete the category "{currentCategory?.title}" and all its items. This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-6 py-3 bg-red-600 text-white font-semibold rounded-xl hover:bg-red-700 transition-all duration-200"
                >
                  Delete Category
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default ModifiersGrid;