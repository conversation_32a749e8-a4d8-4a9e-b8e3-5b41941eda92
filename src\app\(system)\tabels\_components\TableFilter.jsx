import React from 'react'

const TableFilter = ({ selectedArea, onAreaChange,tables }) => {
  const areas = ["All Areas", "INDOOR", "TERRACE"];

  return (
    <div className="mb-6">
      <div className="flex gap-2 justify-between">
        <div className="flex gap-2">
        {areas.map((area) => (
          <button
            key={area}
            onClick={() => onAreaChange(area)}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              selectedArea === area
                ? "text-white shadow-sm"
                : "text-gray-600 bg-white hover:bg-gray-50 border border-gray-200"
            }`}
            style={selectedArea === area ? { backgroundColor: "#FF6500" } : {}}
          >
            {area}
          </button>
        ))}
        </div>
        <button
          onClick={() => alert("Add new modifier")}
          className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4v16m8-8H4"
            />
          </svg>
          إضافة طاوله جديده
        </button>
      </div>

      {/* Area Info */}
      <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
        <span className="font-semibold text-gray-800">
          {selectedArea === "All Areas" ? "ALL AREAS" : selectedArea}
        </span>
        <span>
          {selectedArea === "All Areas"
            ? `${tables.length} Table${tables.length !== 1 ? "s" : ""}`
            : `${tables.filter((t) => t.area === selectedArea).length} Table${
                tables.filter((t) => t.area === selectedArea).length !== 1
                  ? "s"
                  : ""
              }`}
        </span>
      </div>
    </div>
  );
};

export default TableFilter