// // // "use server"
// // // import axios from "axios"

// // // const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// // // // Categories API
// // // export async function fetchCategories() {
// // //     try {
// // //         const res = await axios.get(`${baseURL}/menu/categories`)
// // //         console.log('Categories:', res.data)
// // //         return res.data
// // //     } catch (error) {
// // //         console.error('Error fetching categories:', error)
// // //         throw error
// // //     }
// // // }

// // // // Menu Items API
// // // export async function fetchMenuItems() {
// // //     try {
// // //         const res = await axios.get(`${baseURL}/menu/items`)
// // //         console.log('Menu Items:', res.data)
// // //         return res.data
// // //     } catch (error) {
// // //         console.error('Error fetching menu items:', error)
// // //         throw error
// // //     }
// // // }
// // "use server"
// // import axios from "axios"

// // const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'
// // const bearerToken = '58|SYXGCumLHf15fIJ5ihUObDgMTk9ABomHOOZUpbde4fc5ea53'

// // // Create axios instance with default headers
// // const apiClient = axios.create({
// //     baseURL: baseURL,
// //     headers: {
// //         'Authorization': `Bearer ${bearerToken}`,
// //         'Content-Type': 'application/json',
// //         'Accept': 'application/json'
// //     }
// // })

// // // Categories API
// // export async function fetchCategories() {
// //     try {
// //         const res = await apiClient.get('/menu/categories')
// //         console.log('Categories:', res.data)
// //         return res.data
// //     } catch (error) {
// //         console.error('Error fetching categories:', error)
// //         throw error
// //     }
// // }

// // // Menu Items API
// // export async function fetchMenuItems() {
// //     try {
// //         const res = await apiClient.get('/menu/items')
// //         console.log('Menu Items:', res.data)
// //         return res.data
// //     } catch (error) {
// //         console.error('Error fetching menu items:', error)
// //         throw error
// //     }
// // }
// // // Add this function to your API file
// // export const fetchMenus = async () => {
// //   try {
// //     // Replace with your actual API client
// //     const res = await apiClient.get('/menu/menus')
// //     console.log('Menus:', res.data)
// //     return res.data
// //   } catch (error) {
// //     console.error('Error fetching menus:', error)
// //     throw error
// //   }
// // };
// "use server"
// import axios from "axios"
// import { cookies } from "next/headers"

// const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// // Create base axios instance without token (for login)
// const baseApiClient = axios.create({
//     baseURL: baseURL,
//     headers: {
//         'Content-Type': 'application/json',
//         'Accept': 'application/json'
//     }
// })

// // Function to get token from cookies
// const getStoredToken = async () => {
//     try {
//         const cookieStore = await cookies()
//         return cookieStore.get('auth_token')?.value || null
//     } catch (error) {
//         console.error('Error getting stored token:', error)
//         return null
//     }
// }

// // Create authenticated axios instance with token
// const createAuthenticatedClient = async (token) => {
//     const authToken = token || await getStoredToken()
    
//     return axios.create({
//         baseURL: baseURL,
//         headers: {
//             'Authorization': `Bearer ${authToken}`,
//             'Content-Type': 'application/json',
//             'Accept': 'application/json'
//         }
//     })
// }

// // Categories API - requires authentication
// export async function fetchCategories(token = null) {
//     try {
//         const authClient = await createAuthenticatedClient(token)
//         const res = await authClient.get('/menu/categories')
//         console.log('Categories:', res.data)
//         return res.data
//     } catch (error) {
//         console.error('Error fetching categories:', error)
//         throw error
//     }
// }

// // Menu Items API - requires authentication
// export async function fetchMenuItems(token = null) {
//     try {
//         const authClient = await createAuthenticatedClient(token)
//         const res = await authClient.get('/menu/items')
//         console.log('Menu Items:', res.data)
//         return res.data
//     } catch (error) {
//         console.error('Error fetching menu items:', error)
//         throw error
//     }
// }

// // Menus API - requires authentication
// export const fetchMenus = async (token = null) => {
//     try {
//         const authClient = await createAuthenticatedClient(token)
//         const res = await authClient.get('/menu/menus')
//         console.log('Menus:', res.data)
//         return res.data
//     } catch (error) {
//         console.error('Error fetching menus:', error)
//         throw error
//     }
// }

// // Logout API - requires authentication
// export async function logoutUser(token = null) {
//     try {
//         const authClient = await createAuthenticatedClient(token)
//         const res = await authClient.post('/auth/logout')
//         console.log('Logout response:', res.data)
        
//         // Clear cookies after successful logout
//         const cookieStore = await cookies()
//         cookieStore.delete('auth_token')
//         cookieStore.delete('user_data')
        
//         return {
//             success: true,
//             message: 'تم تسجيل الخروج بنجاح'
//         }
//     } catch (error) {
//         console.error('Error during logout:', error)
//         return {
//             success: false,
//             message: 'فشل تسجيل الخروج'
//         }
//     }
// }
"use client"
import axios from "axios"
import { getAuthToken } from '../src/app/utils/auth' // Adjust path

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// Create base axios instance without token (for login)
const baseApiClient = axios.create({
    baseURL: baseURL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

// Create authenticated axios instance with token
const createAuthenticatedClient = (token) => {
    const authToken = token || getAuthToken()
    
    return axios.create({
        baseURL: baseURL,
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
}

// Categories API - requires authentication
export async function fetchCategories(token = null) {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get('/menu/categories')
        console.log('Categories:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching categories:', error)
        throw error
    }
}

// Menu Items API - requires authentication
export async function fetchMenuItemVariants(menuItemId, token = null) {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get(`/menu/items/${menuItemId}/variants`)
        console.log('Menu Item Variants:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching menu item variants:', error)
        return [] // Return empty array instead of throwing
    }
}

// Fetch addons for a specific menu item
export async function fetchMenuItemAddons(menuItemId, token = null) {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get(`/menu/items/${menuItemId}/addons`)
        console.log('Menu Item Addons:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching menu item addons:', error)
        return [] // Return empty array instead of throwing
    }
}

// Keep the original fetchMenuItems function unchanged
export async function fetchMenuItems(token = null) {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get('/menu/items')
        console.log('Menu Items:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching menu items:', error)
        throw error
    }
}
export async function createMenu(menuData, token = null) {
    try {
        const authClient = createAuthenticatedClient(token);
        const res = await authClient.post('/menu/menus', menuData);
        console.log('Menu created:', res.data);
        return res.data;
    } catch (error) {
        console.error('Error creating menu:', error);
        if (error.response?.data?.errors) {
            // Return validation errors for display
            throw {
                message: error.response.data.message,
                errors: error.response.data.errors
            };
        }
        throw error;
    }
} 

// Menus API - requires authentication
export const fetchMenus = async (token = null) => {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get('api/menu/menus')
        console.log('Menus:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching menus:', error)
        throw error
    }
}

// Areas API - requires authentication
export async function fetchAreas(branchId = 1, token = null) {
    // Ensure this only runs on client side
    if (typeof window === 'undefined') {
        throw new Error('fetchAreas can only be called on the client side')
    }
    
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get(`/reservation/areas?branch_id=${branchId}`)
        console.log('Areas API Response:', res.data)
        
        // Handle the nested response structure
        if (res.data.success && res.data.data && res.data.data.data) {
            return res.data.data.data // Return the actual areas array
        } else {
            throw new Error(res.data.message || 'Failed to fetch areas')
        }
    } catch (error) {
        console.error('Error fetching areas:', error)
        throw error
    }
}

// Create Area API - requires authentication
export async function createArea(areaData, token = null) {
    if (typeof window === 'undefined') {
        throw new Error('createArea can only be called on the client side')
    }
    
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.post('/api/reservation/areas', areaData)
        console.log('Create Area Response:', res.data)
        return res.data
    } catch (error) {
        console.error('Error creating area:', error)
        throw error
    }
}

// Update Area API - requires authentication
export async function updateArea(areaId, areaData, token = null) {
    if (typeof window === 'undefined') {
        throw new Error('updateArea can only be called on the client side')
    }
    
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.put(`/api/reservation/areas/${areaId}`, areaData)
        console.log('Update Area Response:', res.data)
        return res.data
    } catch (error) {
        console.error('Error updating area:', error)
        throw error
    }
}

// Delete Area API - requires authentication
export async function deleteArea(areaId, token = null) {
    if (typeof window === 'undefined') {
        throw new Error('deleteArea can only be called on the client side')
    }
    
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.delete(`/api/reservation/areas/${areaId}`)
        console.log('Delete Area Response:', res.data)
        return res.data
    } catch (error) {
        console.error('Error deleting area:', error)
        throw error
    }
}

// Customers API - requires authentication
export async function fetchCustomers(token = null) {
    try {
        const authClient = createAuthenticatedClient(token)
        const res = await authClient.get('/api/customers')
        console.log('Customers:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching customers:', error)
        throw error
    }
}

// Generic authenticated request function
export const makeAuthenticatedRequest = async (method, endpoint, data = null, token = null) => {
    try {
        const authClient = createAuthenticatedClient(token)
        const config = {
            method,
            url: endpoint,
            ...(data && { data })
        }

        const res = await authClient(config)
        return res.data
    } catch (error) {
        console.error(`Error making ${method} request to ${endpoint}:`, error)
        throw error
    }
}