# Customers Data Table

This directory contains the customers management system for the episys application.

## Overview

The customers data table displays customer information fetched from the API endpoint `https://dev.epi-sys.com/api/customers` and provides a comprehensive view of customer data including:

- Customer personal information (name, email, phone)
- Location details (city, address)
- Loyalty points
- Account status (active/inactive)
- Last visit date
- Customer management actions

## Files Structure

```
customers/
├── _components/
│   └── CustomersGrid.jsx    # Main data table component
├── page.js                  # Page component that renders the grid
└── README.md               # This documentation file
```

## API Integration

### Endpoint
- **URL**: `https://dev.epi-sys.com/api/customers`
- **Method**: GET
- **Authentication**: Bearer token required

### Expected Response Format
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "first_name": "<PERSON>",
      "last_name": "<PERSON>e",
      "email": "<EMAIL>",
      "phone": null,
      "date_of_birth": null,
      "gender": null,
      "address": null,
      "city": null,
      "postal_code": null,
      "loyalty_points": "0.00",
      "preferences": null,
      "notes": null,
      "is_active": true,
      "last_visit_at": null,
      "created_at": "2025-07-20T16:56:23.000000Z",
      "updated_at": "2025-07-20T16:56:23.000000Z"
    }
  ]
}
```

## Features

### Data Display
- **Responsive Table**: Displays customer data in a clean, organized table format
- **Search Functionality**: Real-time search across customer names, email, and phone
- **Status Indicators**: Visual badges for active/inactive customers
- **Loyalty Points**: Display of customer loyalty points with star icon
- **Contact Information**: Email and phone display with appropriate icons
- **Location Info**: City and address information when available

### UI Components
- **Loading State**: Animated spinner while fetching data
- **Error Handling**: User-friendly error messages with retry functionality
- **Empty State**: Informative message when no customers are found
- **Action Buttons**: View, edit, and delete actions for each customer
- **Summary Statistics**: Overview cards showing total customers, active customers, etc.

### Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Desktop Enhanced**: Full feature set on larger screens
- **Horizontal Scroll**: Table scrolls horizontally on smaller screens

## Component Props

The `CustomersGrid` component doesn't accept any props and manages its own state internally.

## State Management

The component uses React hooks for state management:
- `customers`: Array of customer data
- `loading`: Boolean for loading state
- `error`: Error message string
- `searchTerm`: String for search functionality

## Styling

The component uses Tailwind CSS for styling with the following color scheme:
- **Primary Color**: `#FF6500` (Orange)
- **Secondary Colors**: Gray scale for text and backgrounds
- **Status Colors**: Green for active, red for inactive
- **Accent Colors**: Blue for actions, yellow for loyalty points

## Authentication

The component automatically retrieves the authentication token from localStorage and includes it in API requests. If authentication fails, users are redirected to the login page.

## Error Handling

The component includes comprehensive error handling:
- Network errors
- API response errors
- Authentication errors
- Data parsing errors

## Future Enhancements

Potential improvements that could be added:
- Pagination for large datasets
- Advanced filtering options
- Customer creation/editing modals
- Export functionality
- Bulk operations
- Customer details view
- Integration with order history

## Usage

To use this component in other parts of the application:

```jsx
import CustomersGrid from './path/to/CustomersGrid'

function MyPage() {
  return (
    <div>
      <CustomersGrid />
    </div>
  )
}
```

## Dependencies

- React (hooks: useState, useEffect)
- Lucide React (for icons)
- Tailwind CSS (for styling)
- Axios (via lib/api.js for API calls)
