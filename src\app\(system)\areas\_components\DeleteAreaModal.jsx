"use client";
import React, { useState } from "react";
import { X, AlertTriangle } from "lucide-react";

const DeleteAreaModal = ({ isOpen, onClose, area, onAreaDeleted }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleDelete = async () => {
    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'}/api/reservation/areas/${area.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onAreaDeleted(area.id);
        onClose();
      } else {
        setError(data.message || "Failed to delete area");
      }
    } catch (err) {
      console.error('Error deleting area:', err);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError("");
    onClose();
  };

  if (!isOpen || !area) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">حذف المساحة</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="mb-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="text-red-500 mr-3" size={24} />
            <span className="text-lg font-semibold text-gray-800">تأكيد الحذف</span>
          </div>
          
          <p className="text-gray-600 mb-4">
            هل أنت متأكد من أنك تريد حذف المساحة "{area.name}"؟
          </p>
          
          {area.tables && area.tables.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <p className="text-yellow-800 text-sm">
                <strong>تحذير:</strong> هذه المساحة تحتوي على {area.tables.length} طاولة. 
                حذف المساحة سيؤثر على جميع الطاولات المرتبطة بها.
              </p>
            </div>
          )}
          
          <p className="text-red-600 text-sm font-medium">
            هذا الإجراء لا يمكن التراجع عنه.
          </p>
        </div>

        <div className="flex gap-3">
          <button
            type="button"
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            disabled={loading}
          >
            إلغاء
          </button>
          <button
            onClick={handleDelete}
            disabled={loading}
            className="flex-1 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50"
          >
            {loading ? "جاري الحذف..." : "حذف"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteAreaModal;