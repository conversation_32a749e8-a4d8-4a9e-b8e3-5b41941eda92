// // // // // // "use client"
// // // // // // import React, { useState } from 'react';
// // // // // // import { Search, RotateCcw, ShoppingCart, Plus, Minus, User, MapPin, Truck, Coffee, Cookie, Droplets, CreditCard, Printer, Users, Percent, Trash2 } from 'lucide-react';

// // // // // // const Pos = () => {
// // // // // //   const [activeCategory, setActiveCategory] = useState('Show All');
// // // // // //   const [orderType, setOrderType] = useState('Pickup');
// // // // // //   const [cart, setCart] = useState([
// // // // // //     { id: 1, name: 'flat white', size: 'small', price: 50.00, quantity: 1 }
// // // // // //   ]);
// // // // // //   const [searchQuery, setSearchQuery] = useState('');
// // // // // //   const [pax, setPax] = useState(1);
// // // // // //   const [discount, setDiscount] = useState(0);
// // // // // //   const [deliveryTable, setDeliveryTable] = useState('');

// // // // // //   const categories = ['Show All', 'bakery', 'HOT DRINK', 'soft drinks'];

// // // // // //   const menuItems = [
// // // // // //     {
// // // // // //       id: 1,
// // // // // //       name: 'Crowson Turkey',
// // // // // //       price: 55.00,
// // // // // //       category: 'bakery',
// // // // // //       image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 2,
// // // // // //       name: 'Tea',
// // // // // //       price: 20.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 3,
// // // // // //       name: 'Turkish Coffee',
// // // // // //       price: 0,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=300&h=200&fit=crop',
// // // // // //       hasVariations: true
// // // // // //     },
// // // // // //     {
// // // // // //       id: 4,
// // // // // //       name: 'Hot Cedar',
// // // // // //       price: 50.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 5,
// // // // // //       name: 'Cappuccino',
// // // // // //       price: 60.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: '/images/Cappuccino.png',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 6,
// // // // // //       name: 'Arabian Coffee',
// // // // // //       price: 0,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1610889556528-9a770e32642f?w=300&h=200&fit=crop',
// // // // // //       hasVariations: true
// // // // // //     },
// // // // // //     {
// // // // // //       id: 7,
// // // // // //       name: 'Hazelnut Coffee',
// // // // // //       price: 0,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=200&fit=crop',
// // // // // //       hasVariations: true
// // // // // //     },
// // // // // //     {
// // // // // //       id: 8,
// // // // // //       name: 'Nutella Coffee',
// // // // // //       price: 65.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 9,
// // // // // //       name: 'Cinnamon Milk',
// // // // // //       price: 45.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 10,
// // // // // //       name: 'Anise',
// // // // // //       price: 25.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: '/images/Anise.jpg',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 11,
// // // // // //       name: 'Hot Mint',
// // // // // //       price: 25.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=200&fit=crop',
// // // // // //     },
// // // // // //     {
// // // // // //       id: 12,
// // // // // //       name: 'Latte',
// // // // // //       price: 60.00,
// // // // // //       category: 'HOT DRINK',
// // // // // //       image: 'https://images.unsplash.com/photo-1541167760496-1628856ab772?w=300&h=200&fit=crop',
// // // // // //     }
// // // // // //   ];

// // // // // //   const filteredItems = menuItems.filter(item => {
// // // // // //     const matchesCategory = activeCategory === 'Show All' || item.category === activeCategory;
// // // // // //     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
// // // // // //     return matchesCategory && matchesSearch;
// // // // // //   });

// // // // // //   const addToCart = (item) => {
// // // // // //     const existingItem = cart.find(cartItem => cartItem.id === item.id);
// // // // // //     if (existingItem) {
// // // // // //       setCart(cart.map(cartItem =>
// // // // // //         cartItem.id === item.id
// // // // // //           ? { ...cartItem, quantity: cartItem.quantity + 1 }
// // // // // //           : cartItem
// // // // // //       ));
// // // // // //     } else {
// // // // // //       setCart([...cart, { ...item, quantity: 1 }]);
// // // // // //     }
// // // // // //   };

// // // // // //   const removeFromCart = (itemId) => {
// // // // // //     const existingItem = cart.find(cartItem => cartItem.id === itemId);
// // // // // //     if (existingItem && existingItem.quantity > 1) {
// // // // // //       setCart(cart.map(cartItem =>
// // // // // //         cartItem.id === itemId
// // // // // //           ? { ...cartItem, quantity: cartItem.quantity - 1 }
// // // // // //           : cartItem
// // // // // //       ));
// // // // // //     } else {
// // // // // //       setCart(cart.filter(cartItem => cartItem.id !== itemId));
// // // // // //     }
// // // // // //   };

// // // // // //   const deleteFromCart = (itemId) => {
// // // // // //     setCart(cart.filter(cartItem => cartItem.id !== itemId));
// // // // // //   };

// // // // // //   const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0);
// // // // // //   const getTotalPrice = () => cart.reduce((total, item) => total + (item.price * item.quantity), 0);
// // // // // //   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
// // // // // //   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
// // // // // //   const getTax = () => getSubTotal() * 0.14;
// // // // // //   const getFinalTotal = () => getSubTotal() + getTax();

// // // // // //   const resetOrder = () => {
// // // // // //     setCart([]);
// // // // // //     setDiscount(0);
// // // // // //     setPax(1);
// // // // // //     setDeliveryTable('');
// // // // // //     setSearchQuery('');
// // // // // //   };

// // // // // //   return (
// // // // // //     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
// // // // // //       {/* Header */}
// // // // // //       {/* <div className="bg-white shadow-lg border-b border-gray-200">
// // // // // //         <div className="max-w-8xl mx-auto px-4 py-6">
// // // // // //           <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">

// // // // // //             <div className="relative flex-1 max-w-md">
// // // // // //               <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
// // // // // //               <input
// // // // // //                 type="text"
// // // // // //                 placeholder="Search your menu item here"
// // // // // //                 className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none transition-all duration-200"
// // // // // //                 value={searchQuery}
// // // // // //                 onChange={(e) => setSearchQuery(e.target.value)}
// // // // // //               />
// // // // // //             </div>

// // // // // //             <button
// // // // // //               onClick={resetOrder}
// // // // // //               className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-[#FF6500] transition-colors"
// // // // // //             >
// // // // // //               <RotateCcw className="w-4 h-4" />
// // // // // //               Reset
// // // // // //             </button>

// // // // // //             <div className="flex bg-gray-100 rounded-xl p-1">
// // // // // //               {['Dine In', 'Delivery', 'Pickup'].map((type) => (
// // // // // //                 <button
// // // // // //                   key={type}
// // // // // //                   onClick={() => setOrderType(type)}
// // // // // //                   className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
// // // // // //                     orderType === type
// // // // // //                       ? 'bg-[#FF6500] text-white shadow-md'
// // // // // //                       : 'text-gray-600 hover:text-[#FF6500]'
// // // // // //                   }`}
// // // // // //                 >
// // // // // //                   {type}
// // // // // //                 </button>
// // // // // //               ))}
// // // // // //             </div>
// // // // // //           </div>
// // // // // //         </div>
// // // // // //       </div> */}

// // // // // //       <div className="max-w-8xl mx-auto px-4 py-8">
// // // // // //         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
// // // // // //           {/* Menu Section */}
// // // // // //           <div className="lg:col-span-2">
// // // // // //             {/* Categories */}
// // // // // //             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
// // // // // //               {categories.map((category) => (
// // // // // //                 <button
// // // // // //                   key={category}
// // // // // //                   onClick={() => setActiveCategory(category)}
// // // // // //                   className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
// // // // // //                     activeCategory === category
// // // // // //                       ? 'bg-[#FF6500] text-white shadow-lg transform scale-105'
// // // // // //                       : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
// // // // // //                   }`}
// // // // // //                 >
// // // // // //                   {category}
// // // // // //                 </button>
// // // // // //               ))}
// // // // // //             </div>

// // // // // //             {/* Menu Items Grid */}
// // // // // //             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
// // // // // //               {filteredItems.map((item) => (
// // // // // //                 <div
// // // // // //                   key={item.id}
// // // // // //                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer"
// // // // // //                   onClick={() => addToCart(item)}
// // // // // //                 >
// // // // // //                   <div className="relative">
// // // // // //                     <img
// // // // // //                       src={item.image}
// // // // // //                       alt={item.name}
// // // // // //                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
// // // // // //                     />
// // // // // //                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
// // // // // //                   </div>

// // // // // //                   <div className="p-4">
// // // // // //                     <h3 className="text-lg font-bold text-gray-800 mb-1">{item.name}</h3>
// // // // // //                     <div className="flex items-center justify-between">
// // // // // //                       <div className="flex items-center gap-2">
// // // // // //                         <span className="text-lg font-bold text-[#FF6500]">
// // // // // //                           {item.price === 0 ? 'Varies' : `${item.price.toFixed(2)} L.E`}
// // // // // //                         </span>
// // // // // //                       </div>

// // // // // //                       {item.hasVariations && (
// // // // // //                         <button className="text-[#FF6500] text-xs font-medium hover:underline">
// // // // // //                           Variations
// // // // // //                         </button>
// // // // // //                       )}
// // // // // //                     </div>
// // // // // //                   </div>
// // // // // //                 </div>
// // // // // //               ))}
// // // // // //             </div>
// // // // // //           </div>

// // // // // //           {/* Compact Order Summary */}
// // // // // //           <div className="lg:col-span-1">
// // // // // //             <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
// // // // // //               {/* Order Type Pills */}
// // // // // //               <div className="flex justify-center mb-4">
// // // // // //                 {['Dine In', 'Delivery', 'Pickup'].map((type) => (
// // // // // //                   <label key={type} className="flex items-center mx-2">
// // // // // //                     <input
// // // // // //                       type="radio"
// // // // // //                       name="orderType"
// // // // // //                       value={type}
// // // // // //                       checked={orderType === type}
// // // // // //                       onChange={(e) => setOrderType(e.target.value)}
// // // // // //                       className="sr-only"
// // // // // //                     />
// // // // // //                     <div className={`w-3 h-3 rounded-full border-2 mr-2 ${
// // // // // //                       orderType === type
// // // // // //                         ? 'bg-blue-600 border-blue-600'
// // // // // //                         : 'border-gray-300'
// // // // // //                     }`}>
// // // // // //                       {orderType === type && (
// // // // // //                         <div className="w-full h-full rounded-full bg-blue-600 border-2 border-white"></div>
// // // // // //                       )}
// // // // // //                     </div>
// // // // // //                     <span className="text-sm font-medium">{type}</span>
// // // // // //                   </label>
// // // // // //                 ))}
// // // // // //               </div>

// // // // // //               {/* Order Header */}
// // // // // //               <div className="flex items-center justify-between mb-4 pb-2 border-b">
// // // // // //                 <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
// // // // // //               </div>

// // // // // //               {/* Order Items Table */}
// // // // // //               <div className="mb-4">
// // // // // //                 <div className="grid grid-cols-6 gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
// // // // // //                   <div className="col-span-2">ITEM NAME</div>
// // // // // //                   <div className="text-center">QTY</div>
// // // // // //                   <div className="text-center">PRICE</div>
// // // // // //                   <div className="text-center">AMOUNT</div>
// // // // // //                   <div className="text-center">ACTION</div>
// // // // // //                 </div>

// // // // // //                 {cart.length === 0 ? (
// // // // // //                   <div className="text-center py-6 text-gray-500">
// // // // // //                     <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
// // // // // //                     <p className="text-xs">No items in cart</p>
// // // // // //                   </div>
// // // // // //                 ) : (
// // // // // //                   <div className="space-y-2">
// // // // // //                     {cart.map((item) => (
// // // // // //                       <div key={item.id} className="grid grid-cols-6 gap-2 items-center p-2 bg-gray-50 rounded text-xs">
// // // // // //                         <div className="col-span-2">
// // // // // //                           <div className="font-medium">{item.name}</div>
// // // // // //                           {item.size && <div className="text-gray-500">{item.size}</div>}
// // // // // //                         </div>
// // // // // //                         <div className="text-center">
// // // // // //                           <div className="flex items-center justify-center gap-1">
// // // // // //                             <button
// // // // // //                               onClick={() => removeFromCart(item.id)}
// // // // // //                               className="w-5 h-5 bg-gray-300 text-gray-700 rounded flex items-center justify-center hover:bg-gray-400 transition-colors"
// // // // // //                             >
// // // // // //                               <Minus className="w-3 h-3" />
// // // // // //                             </button>
// // // // // //                             <span className="w-6 text-center">{item.quantity}</span>
// // // // // //                             <button
// // // // // //                               onClick={() => addToCart(item)}
// // // // // //                               className="w-5 h-5 bg-[#FF6500] text-white rounded flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// // // // // //                             >
// // // // // //                               <Plus className="w-3 h-3" />
// // // // // //                             </button>
// // // // // //                           </div>
// // // // // //                         </div>
// // // // // //                         <div className="text-center">{item.price.toFixed(2)} L.E</div>
// // // // // //                         <div className="text-center">{(item.price * item.quantity).toFixed(2)} L.E</div>
// // // // // //                         <div className="text-center">
// // // // // //                           <button
// // // // // //                             onClick={() => deleteFromCart(item.id)}
// // // // // //                             className="w-5 h-5 bg-red-500 text-white rounded flex items-center justify-center hover:bg-red-600 transition-colors"
// // // // // //                           >
// // // // // //                             <Trash2 className="w-3 h-3" />
// // // // // //                           </button>
// // // // // //                         </div>
// // // // // //                       </div>
// // // // // //                     ))}
// // // // // //                   </div>
// // // // // //                 )}
// // // // // //               </div>

// // // // // //               {/* Conditional Fields */}
// // // // // //               {orderType === 'Dine In' && (
// // // // // //                 <div className="mb-4">
// // // // // //                   <div className="flex items-center gap-2 mb-2">
// // // // // //                     <Users className="w-4 h-4 text-gray-400" />
// // // // // //                     <span className="text-sm font-medium">Pax:</span>
// // // // // //                     <div className="flex items-center gap-1 ml-auto">
// // // // // //                       <button
// // // // // //                         onClick={() => setPax(Math.max(1, pax - 1))}
// // // // // //                         className="w-6 h-6 bg-red-500 text-white rounded flex items-center justify-center hover:bg-red-600 transition-colors"
// // // // // //                       >
// // // // // //                         <Minus className="w-3 h-3" />
// // // // // //                       </button>
// // // // // //                       <span className="w-8 text-center">{pax}</span>
// // // // // //                       <button
// // // // // //                         onClick={() => setPax(pax + 1)}
// // // // // //                         className="w-6 h-6 bg-[#FF6500] text-white rounded flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// // // // // //                       >
// // // // // //                         <Plus className="w-3 h-3" />
// // // // // //                       </button>
// // // // // //                     </div>
// // // // // //                   </div>
// // // // // //                 </div>
// // // // // //               )}

// // // // // //               {orderType === 'Delivery' && (
// // // // // //                 <div className="mb-4">
// // // // // //                   <div className="flex items-center gap-2 mb-2">
// // // // // //                     <Truck className="w-4 h-4 text-gray-400" />
// // // // // //                     <span className="text-sm font-medium">Delivery Table:</span>
// // // // // //                   </div>
// // // // // //                   <select
// // // // // //                     value={deliveryTable}
// // // // // //                     onChange={(e) => setDeliveryTable(e.target.value)}
// // // // // //                     className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // // // //                   >
// // // // // //                     <option value="">Select Table</option>
// // // // // //                     <option value="table1">Table 1</option>
// // // // // //                     <option value="table2">Table 2</option>
// // // // // //                     <option value="table3">Table 3</option>
// // // // // //                   </select>
// // // // // //                 </div>
// // // // // //               )}

// // // // // //               {/* Add Discount Button */}
// // // // // //               <button
// // // // // //                 onClick={() => setDiscount(discount > 0 ? 0 : 10)}
// // // // // //                 className="w-full mb-4 py-2 px-4 bg-gray-100 border border-gray-300 rounded text-sm hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
// // // // // //               >
// // // // // //                 <Percent className="w-4 h-4" />
// // // // // //                 Add Discount
// // // // // //               </button>

// // // // // //               {/* Order Summary */}
// // // // // //               <div className="space-y-2 mb-4">
// // // // // //                 <div className="flex justify-between text-sm">
// // // // // //                   <span>Item(s)</span>
// // // // // //                   <span>{getTotalItems()}</span>
// // // // // //                 </div>
// // // // // //                 <div className="flex justify-between text-sm">
// // // // // //                   <span>Sub Total</span>
// // // // // //                   <span>{getTotalPrice().toFixed(2)} L.E</span>
// // // // // //                 </div>
// // // // // //                 {discount > 0 && (
// // // // // //                   <div className="flex justify-between text-sm text-green-600">
// // // // // //                     <span>Discount ({discount}%)</span>
// // // // // //                     <span>-{getDiscountAmount().toFixed(2)} L.E</span>
// // // // // //                   </div>
// // // // // //                 )}
// // // // // //                 <div className="flex justify-between text-sm">
// // // // // //                   <span>قيمة مضافة (14%)</span>
// // // // // //                   <span>{getTax().toFixed(2)} L.E</span>
// // // // // //                 </div>
// // // // // //                 <div className="flex justify-between text-lg font-bold border-t pt-2">
// // // // // //                   <span>Total</span>
// // // // // //                   <span>{getFinalTotal().toFixed(2)} L.E</span>
// // // // // //                 </div>
// // // // // //               </div>

// // // // // //               {/* Action Buttons */}
// // // // // //               <div className="space-y-2">
// // // // // //                 <div className="grid grid-cols-2 gap-2">
// // // // // //                   <button className="bg-gray-600 text-white py-2 px-3 rounded text-sm hover:bg-gray-700 transition-colors">
// // // // // //                     KOT
// // // // // //                   </button>
// // // // // //                   <button className="bg-gray-600 text-white py-2 px-3 rounded text-sm hover:bg-gray-700 transition-colors">
// // // // // //                     KOT & Print
// // // // // //                   </button>
// // // // // //                 </div>

// // // // // //                 <button className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors">
// // // // // //                   BILL
// // // // // //                 </button>

// // // // // //                 <div className="grid grid-cols-2 gap-2">
// // // // // //                   <button className="bg-green-600 text-white py-2 px-3 rounded text-sm hover:bg-green-700 transition-colors">
// // // // // //                     Bill & Payment
// // // // // //                   </button>
// // // // // //                   <button className="bg-blue-400 text-white py-2 px-3 rounded text-sm hover:bg-blue-500 transition-colors">
// // // // // //                     Bill & Print
// // // // // //                   </button>
// // // // // //                 </div>
// // // // // //               </div>
// // // // // //             </div>
// // // // // //           </div>
// // // // // //         </div>
// // // // // //       </div>
// // // // // //     </div>
// // // // // //   );
// // // // // // };

// // // // // // export default Pos;
// // // // // "use client"
// // // // // import React, { useState } from 'react';
// // // // // import { Search, RotateCcw, ShoppingCart, Plus, Minus, User, MapPin, Truck, Coffee, Cookie, Droplets, CreditCard, Printer, Users, Percent, Trash2, Grid3X3, Cake } from 'lucide-react';
// // // // // import OrderSummary from './OrderSummary';
// // // // // const Pos = () => {
// // // // //   const [activeCategory, setActiveCategory] = useState('Show All');
// // // // //   const [orderType, setOrderType] = useState('Pickup');
// // // // //   // const [cart, setCart] = useState([
// // // // //   //   { id: 1, name: 'flat white', size: 'small', price: 50.00, quantity: 1 }
// // // // //   // ]);
// // // // //   const [cart, setCart] = useState([]);
// // // // //   const [searchQuery, setSearchQuery] = useState('');
// // // // //   const [pax, setPax] = useState(1);
// // // // //   const [discount, setDiscount] = useState(0);
// // // // //   const [deliveryTable, setDeliveryTable] = useState('');

// // // // //   const categories = [
// // // // //     { name: 'Show All', icon: Grid3X3 },
// // // // //     { name: 'bakery', icon: Cake },
// // // // //     { name: 'HOT DRINK', icon: Coffee },
// // // // //     { name: 'soft drinks', icon: Droplets }
// // // // //   ];

// // // // //   const menuItems = [
// // // // //     {
// // // // //       id: 1,
// // // // //       name: 'Crowson Turkey',
// // // // //       price: 55.00,
// // // // //       category: 'bakery',
// // // // //       image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 2,
// // // // //       name: 'Tea',
// // // // //       price: 20.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 3,
// // // // //       name: 'Turkish Coffee',
// // // // //       price: 0,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=300&h=200&fit=crop',
// // // // //       hasVariations: true
// // // // //     },
// // // // //     {
// // // // //       id: 4,
// // // // //       name: 'Hot Cedar',
// // // // //       price: 50.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 5,
// // // // //       name: 'Cappuccino',
// // // // //       price: 60.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: '/images/Cappuccino.png',
// // // // //     },
// // // // //     {
// // // // //       id: 6,
// // // // //       name: 'Arabian Coffee',
// // // // //       price: 0,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1610889556528-9a770e32642f?w=300&h=200&fit=crop',
// // // // //       hasVariations: true
// // // // //     },
// // // // //     {
// // // // //       id: 7,
// // // // //       name: 'Hazelnut Coffee',
// // // // //       price: 0,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=200&fit=crop',
// // // // //       hasVariations: true
// // // // //     },
// // // // //     {
// // // // //       id: 8,
// // // // //       name: 'Nutella Coffee',
// // // // //       price: 65.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 9,
// // // // //       name: 'Cinnamon Milk',
// // // // //       price: 45.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 10,
// // // // //       name: 'Anise',
// // // // //       price: 25.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: '/images/Anise.jpg',
// // // // //     },
// // // // //     {
// // // // //       id: 11,
// // // // //       name: 'Hot Mint',
// // // // //       price: 25.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=200&fit=crop',
// // // // //     },
// // // // //     {
// // // // //       id: 12,
// // // // //       name: 'Latte',
// // // // //       price: 60.00,
// // // // //       category: 'HOT DRINK',
// // // // //       image: 'https://images.unsplash.com/photo-1541167760496-1628856ab772?w=300&h=200&fit=crop',
// // // // //     }
// // // // //   ];

// // // // //   const filteredItems = menuItems.filter(item => {
// // // // //     const matchesCategory = activeCategory === 'Show All' || item.category === activeCategory;
// // // // //     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
// // // // //     return matchesCategory && matchesSearch;
// // // // //   });

// // // // //   const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0);
// // // // //   const getTotalPrice = () => cart.reduce((total, item) => total + (item.price * item.quantity), 0);
// // // // //   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
// // // // //   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
// // // // //   const getTax = () => getSubTotal() * 0.14;
// // // // //   const getFinalTotal = () => getSubTotal() + getTax();

// // // // //   const resetOrder = () => {
// // // // //     setCart([]);
// // // // //     setDiscount(0);
// // // // //     setPax(1);
// // // // //     setDeliveryTable('');
// // // // //     setSearchQuery('');
// // // // //   };
// // // // // const addToCart = (item) => {
// // // // //   const existingItem = cart.find(cartItem => cartItem.id === item.id);
// // // // //   if (existingItem) {
// // // // //     setCart(cart.map(cartItem =>
// // // // //       cartItem.id === item.id
// // // // //         ? { ...cartItem, quantity: cartItem.quantity + 1 }
// // // // //         : cartItem
// // // // //     ));
// // // // //   } else {
// // // // //     setCart([...cart, { ...item, quantity: 1, comment: '' }]);
// // // // //   }
// // // // // };

// // // // // const removeFromCart = (itemId) => {
// // // // //   setCart(prev => prev.map(item =>
// // // // //     item.id === itemId && item.quantity > 1
// // // // //       ? { ...item, quantity: item.quantity - 1 }
// // // // //       : item
// // // // //   ));
// // // // // };

// // // // // const deleteFromCart = (itemId) => {
// // // // //   setCart(prev => prev.filter(item => item.id !== itemId));
// // // // // };
// // // // //   return (
// // // // //     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
// // // // //       <div className="max-w-8xl mx-auto px-4 py-8">
// // // // //         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
// // // // //           {/* Menu Section */}
// // // // //           <div className="lg:col-span-2">
// // // // //             {/* Categories */}
// // // // //             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
// // // // //               {categories.map((category) => {
// // // // //                 const IconComponent = category.icon;
// // // // //                 return (
// // // // //                   <button
// // // // //                     key={category.name}
// // // // //                     onClick={() => setActiveCategory(category.name)}
// // // // //                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
// // // // //                       activeCategory === category.name
// // // // //                         ? 'bg-[#FF6500] text-white shadow-lg transform scale-105'
// // // // //                         : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
// // // // //                     }`}
// // // // //                   >
// // // // //                     <IconComponent className="w-5 h-5" />
// // // // //                     {category.name}
// // // // //                   </button>
// // // // //                 );
// // // // //               })}
// // // // //             </div>

// // // // //             {/* Menu Items Grid */}
// // // // //             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
// // // // //               {filteredItems.map((item) => (
// // // // //                 <div
// // // // //                   key={item.id}
// // // // //                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer"
// // // // //                   onClick={() => addToCart(item)}
// // // // //                 >
// // // // //                   <div className="relative">
// // // // //                     <img
// // // // //                       src={item.image}
// // // // //                       alt={item.name}
// // // // //                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
// // // // //                     />
// // // // //                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
// // // // //                   </div>

// // // // //                   <div className="p-4">
// // // // //                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">{item.name}</h3>
// // // // //                     <div className="flex items-center justify-between">
// // // // //                       <div className="flex items-center gap-2">
// // // // //                         <span className="text-lg font-bold text-[#FF6500]">
// // // // //                           {item.price === 0 ? 'Varies' : `${item.price.toFixed(2)} L.E`}
// // // // //                         </span>
// // // // //                       </div>

// // // // //                       {item.hasVariations && (
// // // // //                         <button className="text-[#FF6500] text-xs font-medium hover:underline">
// // // // //                           Variations
// // // // //                         </button>
// // // // //                       )}
// // // // //                     </div>
// // // // //                   </div>
// // // // //                 </div>
// // // // //               ))}
// // // // //             </div>
// // // // //           </div>

// // // // //           {/* Compact Order Summary */}
// // // // //           {/* <OrderSummary /> */}
// // // // //           <OrderSummary
// // // // //             cart={cart}
// // // // //             setCart={setCart}
// // // // //             addToCart={addToCart}
// // // // //             removeFromCart={removeFromCart}
// // // // //             deleteFromCart={deleteFromCart}
// // // // //           />
// // // // //         </div>
// // // // //       </div>
// // // // //     </div>
// // // // //   );
// // // // // };

// // // // // export default Pos;
// // // // "use client";
// // // // import React, { useState } from "react";
// // // // import {
// // // //   Search,
// // // //   RotateCcw,
// // // //   ShoppingCart,
// // // //   Plus,
// // // //   Minus,
// // // //   User,
// // // //   MapPin,
// // // //   Truck,
// // // //   Coffee,
// // // //   Cookie,
// // // //   Droplets,
// // // //   CreditCard,
// // // //   Printer,
// // // //   Users,
// // // //   Percent,
// // // //   Trash2,
// // // //   Grid3X3,
// // // //   Cake,
// // // //   X,
// // // //   MessageSquare,
// // // //   Edit3,
// // // //   Phone,
// // // // } from "lucide-react";
// // // // import OrderSummary from "./OrderSummary";
// // // // const Pos = () => {
// // // //   const [activeCategory, setActiveCategory] = useState("Show All");
// // // //   const [orderType, setOrderType] = useState("Pickup");
// // // //   const [cart, setCart] = useState([]);
// // // //   const [searchQuery, setSearchQuery] = useState("");
// // // //   const [pax, setPax] = useState(1);
// // // //   const [discount, setDiscount] = useState(0);
// // // //   const [deliveryTable, setDeliveryTable] = useState("");
// // // //   const [showVariantModal, setShowVariantModal] = useState(false);
// // // //   const [selectedProduct, setSelectedProduct] = useState(null);
// // // //   const [selectedAddons, setSelectedAddons] = useState({});
// // // //   const categories = [
// // // //     { name: "Show All", icon: Grid3X3 },
// // // //     { name: "bakery", icon: Cake },
// // // //     { name: "HOT DRINK", icon: Coffee },
// // // //     { name: "soft drinks", icon: Droplets },
// // // //   ];

// // // //   const menuItems = [
// // // //     {
// // // //       id: 1,
// // // //       name: "Crowson Turkey",
// // // //       price: 55.0,
// // // //       category: "bakery",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1509440159596-0249088772ff?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //       id: 2,
// // // //       name: "Tea",
// // // //       price: 20.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //     id: 3,
// // // //     name: "Turkish Coffee",
// // // //     price: 0,
// // // //     category: "HOT DRINK",
// // // //     image: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=300&h=200&fit=crop",
// // // //     hasVariations: true,
// // // //     variants: [
// // // //       { id: "tc_single", name: "Single", price: 35.0 },
// // // //       { id: "tc_double", name: "Double", price: 45.0 },
// // // //       { id: "tc_large", name: "Large", price: 55.0 },
// // // //     ],
// // // //     //  addons 
// // // //     addons: [
// // // //       { id: "sugar", name: "Extra Sugar", price: 2.0 },
// // // //       { id: "milk", name: "Extra Milk", price: 5.0 },
// // // //       { id: "cream", name: "Whipped Cream", price: 8.0 },
// // // //       { id: "cinnamon", name: "Cinnamon", price: 3.0 }
// // // //     ]
// // // //   },
// // // //     {
// // // //       id: 4,
// // // //       name: "Hot Cedar",
// // // //       price: 50.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //       id: 5,
// // // //       name: "Cappuccino",
// // // //       price: 60.0,
// // // //       category: "HOT DRINK",
// // // //       image: "/images/Cappuccino.png",
// // // //     },
// // // //     {
// // // //       id: 6,
// // // //       name: "Arabian Coffee",
// // // //       price: 0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1610889556528-9a770e32642f?w=300&h=200&fit=crop",
// // // //       hasVariations: true,
// // // //       variants: [
// // // //         { id: "ac_single", name: "Single", price: 40.0 },
// // // //         { id: "ac_double", name: "Double", price: 50.0 },
// // // //       ],
// // // //     },
// // // //     {
// // // //       id: 7,
// // // //       name: "Hazelnut Coffee",
// // // //       price: 0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=200&fit=crop",
// // // //       hasVariations: true,
// // // //       variants: [
// // // //         { id: "hc_small", name: "Small", price: 45.0 },
// // // //         { id: "hc_medium", name: "Medium", price: 55.0 },
// // // //         { id: "hc_large", name: "Large", price: 65.0 },
// // // //       ],
// // // //     },
// // // //     {
// // // //       id: 8,
// // // //       name: "Nutella Coffee",
// // // //       price: 65.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //       id: 9,
// // // //       name: "Cinnamon Milk",
// // // //       price: 45.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //       id: 10,
// // // //       name: "Anise",
// // // //       price: 25.0,
// // // //       category: "HOT DRINK",
// // // //       image: "/images/Anise.jpg",
// // // //     },
// // // //     {
// // // //       id: 11,
// // // //       name: "Hot Mint",
// // // //       price: 25.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=200&fit=crop",
// // // //     },
// // // //     {
// // // //       id: 12,
// // // //       name: "Latte",
// // // //       price: 60.0,
// // // //       category: "HOT DRINK",
// // // //       image:
// // // //         "https://images.unsplash.com/photo-1541167760496-1628856ab772?w=300&h=200&fit=crop",
// // // //     },
// // // //   ];

// // // //   const filteredItems = menuItems.filter((item) => {
// // // //     const matchesCategory =
// // // //       activeCategory === "Show All" || item.category === activeCategory;
// // // //     const matchesSearch = item.name
// // // //       .toLowerCase()
// // // //       .includes(searchQuery.toLowerCase());
// // // //     return matchesCategory && matchesSearch;
// // // //   });

// // // //   const getTotalItems = () =>
// // // //     cart.reduce((total, item) => total + item.quantity, 0);
// // // //   const getTotalPrice = () =>
// // // //     cart.reduce((total, item) => total + item.price * item.quantity, 0);
// // // //   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
// // // //   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
// // // //   const getTax = () => getSubTotal() * 0.14;
// // // //   const getFinalTotal = () => getSubTotal() + getTax();

// // // //   const resetOrder = () => {
// // // //     setCart([]);
// // // //     setDiscount(0);
// // // //     setPax(1);
// // // //     setDeliveryTable("");
// // // //     setSearchQuery("");
// // // //   };

// // // //   const handleProductClick = (item) => {
// // // //     if (item.hasVariations && item.variants) {
// // // //       setSelectedProduct(item);
// // // //       setShowVariantModal(true);
// // // //     } else {
// // // //       addToCart(item);
// // // //     }
// // // //   };
// // // //   const addToCart = (item, variant = null, addons = []) => {
// // // //   // حساب سعر الـ addons
// // // //   const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
  
// // // //   const cartItem = variant
// // // //     ? {
// // // //         ...item,
// // // //         id: `${item.id}_${variant.id}`, 
// // // //         name: `${item.name} - ${variant.name}`,
// // // //         price: variant.price + addonsPrice, // إضافة سعر الـ addons
// // // //         originalPrice: variant.price, // الاحتفاظ بالسعر الأصلي
// // // //         originalId: item.id,
// // // //         variantId: variant.id,
// // // //         variantName: variant.name,
// // // //         image: item.image,
// // // //         size: variant.name,
// // // //         addons: addons // إضافة الـ addons
// // // //       }
// // // //     : { 
// // // //         ...item, 
// // // //         image: item.image,
// // // //         price: item.price + addonsPrice,
// // // //         originalPrice: item.price,
// // // //         addons: addons
// // // //       };

// // // //   const existingItem = cart.find(
// // // //     (existingCartItem) => existingCartItem.id === cartItem.id
// // // //   );

// // // //   if (existingItem) {
// // // //     setCart(
// // // //       cart.map((existingCartItem) =>
// // // //         existingCartItem.id === cartItem.id
// // // //           ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
// // // //           : existingCartItem
// // // //       )
// // // //     );
// // // //   } else {
// // // //     setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
// // // //   }

// // // //   if (variant) {
// // // //     setShowVariantModal(false);
// // // //     setSelectedProduct(null);
// // // //   }
// // // // };
// // // //   const removeFromCart = (itemId) => {
// // // //     setCart((prev) =>
// // // //       prev.map((item) =>
// // // //         item.id === itemId && item.quantity > 1
// // // //           ? { ...item, quantity: item.quantity - 1 }
// // // //           : item
// // // //       )
// // // //     );
// // // //   };

// // // //   const deleteFromCart = (itemId) => {
// // // //     setCart((prev) => prev.filter((item) => item.id !== itemId));
// // // //   };

// // // //  const VariantModal = () => {
// // // //   const [selectedAddons, setSelectedAddons] = useState({});

// // // //   if (!showVariantModal || !selectedProduct) return null;

// // // //   const handleAddonChange = (addonId, quantity) => {
// // // //     setSelectedAddons(prev => ({
// // // //       ...prev,
// // // //       [addonId]: quantity
// // // //     }));
// // // //   };

// // // //   const addProductWithAddons = (product, variant) => {
// // // //     // تحويل الـ addons المختارة إلى array
// // // //     const selectedAddonsArray = Object.entries(selectedAddons)
// // // //       .filter(([_, quantity]) => quantity > 0)
// // // //       .map(([addonId, quantity]) => {
// // // //         const addon = product.addons.find(a => a.id === addonId);
// // // //         return {
// // // //           ...addon,
// // // //           quantity: quantity
// // // //         };
// // // //       });

// // // //     // إضافة المنتج مع الـ addons
// // // //     addToCart(product, variant, selectedAddonsArray);
    
// // // //     // إعادة تعيين الـ addons
// // // //     setSelectedAddons({});
// // // //   };

// // // //   return (
// // // //       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
// // // //         <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
// // // //         <div className="relative p-6 border-b border-gray-100">
// // // //           <button
// // // //             onClick={() => {
// // // //               setShowVariantModal(false);
// // // //               setSelectedProduct(null);
// // // //               setSelectedAddons({});
// // // //             }}
// // // //             className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
// // // //           >
// // // //             <X className="w-5 h-5 text-gray-500" />
// // // //           </button>
// // // //           <h3 className="text-xl font-bold text-gray-900 mb-2">
// // // //             Customize Your Order
// // // //           </h3>
// // // //           <p className="text-sm text-gray-600">Choose size and add extras</p>
// // // //         </div>
// // // //           {/* Product Info */}
// // // //           <div className="p-6 border-b border-gray-100">
// // // //             <div className="flex items-center gap-4">
// // // //               <img
// // // //                 src={selectedProduct.image}
// // // //                 alt={selectedProduct.name}
// // // //                 className="w-16 h-16 rounded-lg object-cover shadow-md"
// // // //               />
// // // //               <div>
// // // //                 <h4 className="font-semibold text-gray-900 text-lg">
// // // //                   {selectedProduct.name}
// // // //                 </h4>
// // // //                 <p className="text-sm text-gray-500">
// // // //                   Available in multiple sizes
// // // //                 </p>
// // // //               </div>
// // // //             </div>

// // // //           <div className="overflow-y-auto flex justify-center items-start flex-wrap">
// // // //             {/* Variants Section  */}
// // // //             {selectedProduct.variants && (
// // // //               <div className="p-6 border-b border-gray-100 lg:w-1/3 lg:flex-1">
// // // //                 <h5 className="font-semibold text-gray-900 mb-3">Choose Size</h5>
// // // //                 <div className="space-y-3">
// // // //                   {selectedProduct.variants.map((variant) => (
// // // //                     <div
// // // //                       key={variant.id}
// // // //                       className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
// // // //                       onClick={() => addProductWithAddons(selectedProduct, variant)}
// // // //                     >
// // // //                       <div className="flex-1">
// // // //                         <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
// // // //                           {variant.name}
// // // //                         </h6>
// // // //                       </div>
// // // //                       <div className="text-right">
// // // //                         <p className="font-bold text-lg text-[#FF6500]">
// // // //                           {variant.price.toFixed(2)} L.E
// // // //                         </p>
// // // //                       </div>
// // // //                     </div>
// // // //                   ))}
// // // //                 </div>
// // // //               </div>
// // // //             )}

// // // //             {/* Addons Section */}
// // // //             {selectedProduct.addons && selectedProduct.addons.length > 0 && (
// // // //               <div className="p-6 lg:w-1/3 lg:flex-1">
// // // //                 <h5 className="font-semibold text-gray-900 mb-3">Add Extras</h5>
// // // //                 <div className="space-y-3">
// // // //                   {selectedProduct.addons.map((addon) => (
// // // //                     <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
// // // //                       <div className="flex-1">
// // // //                         <h6 className="font-medium text-gray-900">{addon.name}</h6>
// // // //                         <p className="text-sm text-[#FF6500] font-semibold">
// // // //                           +{addon.price.toFixed(2)} L.E
// // // //                         </p>
// // // //                       </div>
// // // //                       <div className="flex items-center gap-2">
// // // //                         <button
// // // //                           onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
// // // //                           className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
// // // //                           disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
// // // //                         >
// // // //                           <Minus className="w-4 h-4" />
// // // //                         </button>
// // // //                         <span className="w-8 text-center font-medium">
// // // //                           {selectedAddons[addon.id] || 0}
// // // //                         </span>
// // // //                         <button
// // // //                           onClick={() => handleAddonChange(addon.id, (selectedAddons[addon.id] || 0) + 1)}
// // // //                           className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors"
// // // //                         >
// // // //                           <Plus className="w-4 h-4" />
// // // //                         </button>
// // // //                       </div>
// // // //                     </div>
// // // //                   ))}
// // // //                 </div>
// // // //               </div>
// // // //             )}
// // // //           </div>

// // // //             </div>
// // // //       </div>
// // // //     </div>
// // // //   );
// // // // };
// // // //   return (
// // // //     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
// // // //       <div className="max-w-8xl mx-auto px-4 py-8">
// // // //         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
// // // //           {/* Menu Section */}
// // // //           <div className="lg:col-span-2">
// // // //             {/* Search Bar */}
// // // //             <div className="relative mb-6">
// // // //               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
// // // //               <input
// // // //                 type="text"
// // // //                 placeholder="Search menu items..."
// // // //                 value={searchQuery}
// // // //                 onChange={(e) => setSearchQuery(e.target.value)}
// // // //                 className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// // // //               />
// // // //             </div>

// // // //             {/* Categories */}
// // // //             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
// // // //               {categories.map((category) => {
// // // //                 const IconComponent = category.icon;
// // // //                 return (
// // // //                   <button
// // // //                     key={category.name}
// // // //                     onClick={() => setActiveCategory(category.name)}
// // // //                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
// // // //                       activeCategory === category.name
// // // //                         ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
// // // //                         : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
// // // //                     }`}
// // // //                   >
// // // //                     <IconComponent className="w-5 h-5" />
// // // //                     {category.name}
// // // //                   </button>
// // // //                 );
// // // //               })}
// // // //             </div>

// // // //             {/* Menu Items Grid */}
// // // //             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
// // // //               {filteredItems.map((item) => (
// // // //                 <div
// // // //                   key={item.id}
// // // //                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
// // // //                   onClick={() => handleProductClick(item)}
// // // //                 >
// // // //                   {/* Variant Badge */}
// // // //                   {item.hasVariations && (
// // // //                     <div className="absolute top-3 right-3 z-10">
// // // //                       <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
// // // //                         <div className="flex items-center gap-1">
// // // //                           <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
// // // //                           Variant
// // // //                         </div>
// // // //                       </div>
// // // //                     </div>
// // // //                   )}

// // // //                   <div className="relative">
// // // //                     <img
// // // //                       src={item.image}
// // // //                       alt={item.name}
// // // //                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
// // // //                     />
// // // //                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
// // // //                   </div>

// // // //                   <div className="p-4">
// // // //                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
// // // //                       {item.name}
// // // //                     </h3>
// // // //                     <div className="flex items-center justify-between">
// // // //                       <div className="flex items-center gap-2">
// // // //                         <span className="text-lg font-bold text-[#FF6500]">
// // // //                              {item.price === 0
// // // //                             ? "depends on variant"
// // // //                             : `${item.price.toFixed(2)} L.E`}
// // // //                         </span>
// // // //                       </div>
// // // //                     </div>
// // // //                   </div>
// // // //                 </div>
// // // //               ))}
// // // //             </div>
// // // //           </div>

// // // //           {/* Order Summary */}
// // // //           <OrderSummary
// // // //             cart={cart}
// // // //             setCart={setCart}
// // // //             addToCart={addToCart}
// // // //             removeFromCart={removeFromCart}
// // // //             deleteFromCart={deleteFromCart}
// // // //           />
// // // //         </div>
// // // //       </div>

// // // //       {/* Variant Modal */}
// // // //       <VariantModal />
// // // //     </div>
// // // //   );
// // // // };

// // // // export default Pos;
// // // "use client";
// // // import React, { useState, useEffect } from "react";
// // // import {
// // //   Search,
// // //   RotateCcw,
// // //   ShoppingCart,
// // //   Plus,
// // //   Minus,
// // //   User,
// // //   MapPin,
// // //   Truck,
// // //   Coffee,
// // //   Cookie,
// // //   Droplets,
// // //   CreditCard,
// // //   Printer,
// // //   Users,
// // //   Percent,
// // //   Trash2,
// // //   Grid3X3,
// // //   Cake,
// // //   X,
// // //   MessageSquare,
// // //   Edit3,
// // //   Phone,
// // // } from "lucide-react";
// // // import OrderSummary from "./OrderSummary";
// // // import { fetchMenuItems, fetchCategories, fetchMenus } from '../../../../../lib/api';

// // // const Pos = ({ token = null }) => {
// // //   const [activeCategory, setActiveCategory] = useState("Show All");
// // //   const [orderType, setOrderType] = useState("Pickup");
// // //   const [cart, setCart] = useState([]);
// // //   const [searchQuery, setSearchQuery] = useState("");
// // //   const [pax, setPax] = useState(1);
// // //   const [discount, setDiscount] = useState(0);
// // //   const [deliveryTable, setDeliveryTable] = useState("");
// // //   const [showVariantModal, setShowVariantModal] = useState(false);
// // //   const [selectedProduct, setSelectedProduct] = useState(null);
// // //   const [selectedAddons, setSelectedAddons] = useState({});
  
// // //   // API Data States
// // //   const [categories, setCategories] = useState([]);
// // //   const [menuItems, setMenuItems] = useState([]);
// // //   const [menus, setMenus] = useState([]);
// // //   const [loading, setLoading] = useState(true);
// // //   const [error, setError] = useState(null);
// // //   const [selectedMenu, setSelectedMenu] = useState(null);

// // //   // Helper function to get icons for categories
// // //   function getIconForCategory(categoryName) {
// // //     const name = categoryName.toLowerCase();
// // //     if (name.includes('drink') || name.includes('beverage')) return Coffee;
// // //     if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
// // //     if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
// // //     return Cookie; // default icon
// // //   }

// // //   // Load data on component mount
// // //   useEffect(() => {
// // //     const loadData = async () => {
// // //       try {
// // //         setLoading(true);
// // //         setError(null);

// // //         const [categoriesData, menuItemsData, menusData] = await Promise.all([
// // //           fetchCategories(token),
// // //           fetchMenuItems(token),
// // //           fetchMenus(token)
// // //         ]);

// // //         setCategories(categoriesData);
// // //         setMenuItems(menuItemsData);
// // //         setMenus(menusData);

// // //         // Set default menu (first active menu or first menu)
// // //         const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
// // //         setSelectedMenu(defaultMenu);

// // //       } catch (err) {
// // //         console.error('Error loading data:', err);
// // //         setError('Failed to load menu data');
// // //       } finally {
// // //         setLoading(false);
// // //       }
// // //     };

// // //     loadData();
// // //   }, [token]);

// // //   // Transform API categories to match your existing structure
// // //   const transformedCategories = [
// // //     { name: "Show All", icon: Grid3X3 },
// // //     ...categories
// // //       .filter(cat => cat.is_active)
// // //       .map(cat => ({
// // //         name: cat.name,
// // //         icon: getIconForCategory(cat.name),
// // //         id: cat.id,
// // //         code: cat.code
// // //       }))
// // //   ];

// // //   // Helper function to get icons for categories
// // //   function getIconForCategory(categoryName) {
// // //     const name = categoryName.toLowerCase();
// // //     if (name.includes('drink') || name.includes('beverage')) return Coffee;
// // //     if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
// // //     if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
// // //     return Cookie; // default icon
// // //   }

// // //   // Transform API menu items to match your existing structure
// // //   const transformedMenuItems = menuItems
// // //     .filter(item => item.is_active)
// // //     .map(item => ({
// // //       id: item.id,
// // //       name: item.name,
// // //       price: parseFloat(item.base_price),
// // //       category: item.category?.name || 'Other',
// // //       categoryId: item.category_id,
// // //       image: item.image_urls && item.image_urls.length > 0 
// // //         ? item.image_urls[0] 
// // //         : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
// // //       description: item.description,
// // //       shortDescription: item.short_description,
// // //       hasVariations: item.variants && item.variants.length > 0,
// // //       variants: item.variants?.map(variant => ({
// // //         id: variant.id,
// // //         name: variant.name,
// // //         price: parseFloat(variant.price)
// // //       })) || [],
// // //       addons: item.addons?.map(addon => ({
// // //         id: addon.id,
// // //         name: addon.name,
// // //         price: parseFloat(addon.price)
// // //       })) || [],
// // //       allergens: item.allergens || [],
// // //       dietaryInfo: item.dietary_info || [],
// // //       isSpicy: item.is_spicy,
// // //       prepTime: item.prep_time_minutes,
// // //       calories: item.calories
// // //     }));

// // //   // Filter items based on selected menu, category, and search
// // //   const filteredItems = transformedMenuItems.filter((item) => {
// // //     // Filter by selected menu if specified
// // //     if (selectedMenu && item.menu_id && item.menu_id !== selectedMenu.id) {
// // //       return false;
// // //     }

// // //     // Filter by category
// // //     const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
// // //     // Filter by search query
// // //     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
// // //     return matchesCategory && matchesSearch;
// // //   });

// // //   const getTotalItems = () =>
// // //     cart.reduce((total, item) => total + item.quantity, 0);
// // //   const getTotalPrice = () =>
// // //     cart.reduce((total, item) => total + item.price * item.quantity, 0);
// // //   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
// // //   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
// // //   const getTax = () => getSubTotal() * 0.14;
// // //   const getFinalTotal = () => getSubTotal() + getTax();

// // //   const resetOrder = () => {
// // //     setCart([]);
// // //     setDiscount(0);
// // //     setPax(1);
// // //     setDeliveryTable("");
// // //     setSearchQuery("");
// // //   };

// // //   const handleProductClick = (item) => {
// // //     if (item.hasVariations && item.variants && item.variants.length > 0) {
// // //       setSelectedProduct(item);
// // //       setShowVariantModal(true);
// // //     } else {
// // //       addToCart(item);
// // //     }
// // //   };

// // //   const addToCart = (item, variant = null, addons = []) => {
// // //     // Calculate addons price
// // //     const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
// // //     const cartItem = variant
// // //       ? {
// // //           ...item,
// // //           id: `${item.id}_${variant.id}`, 
// // //           name: `${item.name} - ${variant.name}`,
// // //           price: variant.price + addonsPrice,
// // //           originalPrice: variant.price,
// // //           originalId: item.id,
// // //           variantId: variant.id,
// // //           variantName: variant.name,
// // //           image: item.image,
// // //           size: variant.name,
// // //           addons: addons
// // //         }
// // //       : { 
// // //           ...item, 
// // //           image: item.image,
// // //           price: item.price + addonsPrice,
// // //           originalPrice: item.price,
// // //           addons: addons
// // //         };

// // //     const existingItem = cart.find(
// // //       (existingCartItem) => existingCartItem.id === cartItem.id
// // //     );

// // //     if (existingItem) {
// // //       setCart(
// // //         cart.map((existingCartItem) =>
// // //           existingCartItem.id === cartItem.id
// // //             ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
// // //             : existingCartItem
// // //         )
// // //       );
// // //     } else {
// // //       setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
// // //     }

// // //     if (variant) {
// // //       setShowVariantModal(false);
// // //       setSelectedProduct(null);
// // //     }
// // //   };

// // //   const removeFromCart = (itemId) => {
// // //     setCart((prev) =>
// // //       prev.map((item) =>
// // //         item.id === itemId && item.quantity > 1
// // //           ? { ...item, quantity: item.quantity - 1 }
// // //           : item
// // //       )
// // //     );
// // //   };

// // //   const deleteFromCart = (itemId) => {
// // //     setCart((prev) => prev.filter((item) => item.id !== itemId));
// // //   };

// // //   const VariantModal = () => {
// // //     const [selectedAddons, setSelectedAddons] = useState({});

// // //     if (!showVariantModal || !selectedProduct) return null;

// // //     const handleAddonChange = (addonId, quantity) => {
// // //       setSelectedAddons(prev => ({
// // //         ...prev,
// // //         [addonId]: quantity
// // //       }));
// // //     };

// // //     const addProductWithAddons = (product, variant) => {
// // //       const selectedAddonsArray = Object.entries(selectedAddons)
// // //         .filter(([_, quantity]) => quantity > 0)
// // //         .map(([addonId, quantity]) => {
// // //           const addon = product.addons.find(a => a.id === addonId);
// // //           return {
// // //             ...addon,
// // //             quantity: quantity
// // //           };
// // //         });

// // //       addToCart(product, variant, selectedAddonsArray);
// // //       setSelectedAddons({});
// // //     };

// // //     return (
// // //       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
// // //         <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
// // //           <div className="relative p-6 border-b border-gray-100">
// // //             <button
// // //               onClick={() => {
// // //                 setShowVariantModal(false);
// // //                 setSelectedProduct(null);
// // //                 setSelectedAddons({});
// // //               }}
// // //               className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
// // //             >
// // //               <X className="w-5 h-5 text-gray-500" />
// // //             </button>
// // //             <h3 className="text-xl font-bold text-gray-900 mb-2">
// // //               Customize Your Order
// // //             </h3>
// // //             <p className="text-sm text-gray-600">Choose size and add extras</p>
// // //           </div>

// // //           <div className="p-6 border-b border-gray-100">
// // //             <div className="flex items-center gap-4">
// // //               <img
// // //                 src={selectedProduct.image}
// // //                 alt={selectedProduct.name}
// // //                 className="w-16 h-16 rounded-lg object-cover shadow-md"
// // //               />
// // //               <div>
// // //                 <h4 className="font-semibold text-gray-900 text-lg">
// // //                   {selectedProduct.name}
// // //                 </h4>
// // //                 <p className="text-sm text-gray-500">
// // //                   {selectedProduct.shortDescription || "Available in multiple sizes"}
// // //                 </p>
// // //               </div>
// // //             </div>

// // //             <div className="overflow-y-auto flex justify-center items-start flex-wrap">
// // //               {/* Variants Section */}
// // //               {selectedProduct.variants && selectedProduct.variants.length > 0 && (
// // //                 <div className="p-6 border-b border-gray-100 lg:w-1/3 lg:flex-1">
// // //                   <h5 className="font-semibold text-gray-900 mb-3">Choose Size</h5>
// // //                   <div className="space-y-3">
// // //                     {selectedProduct.variants.map((variant) => (
// // //                       <div
// // //                         key={variant.id}
// // //                         className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
// // //                         onClick={() => addProductWithAddons(selectedProduct, variant)}
// // //                       >
// // //                         <div className="flex-1">
// // //                           <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
// // //                             {variant.name}
// // //                           </h6>
// // //                         </div>
// // //                         <div className="text-right">
// // //                           <p className="font-bold text-lg text-[#FF6500]">
// // //                             {variant.price.toFixed(2)} L.E
// // //                           </p>
// // //                         </div>
// // //                       </div>
// // //                     ))}
// // //                   </div>
// // //                 </div>
// // //               )}

// // //               {/* Addons Section */}
// // //               {selectedProduct.addons && selectedProduct.addons.length > 0 && (
// // //                 <div className="p-6 lg:w-1/3 lg:flex-1">
// // //                   <h5 className="font-semibold text-gray-900 mb-3">Add Extras</h5>
// // //                   <div className="space-y-3">
// // //                     {selectedProduct.addons.map((addon) => (
// // //                       <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
// // //                         <div className="flex-1">
// // //                           <h6 className="font-medium text-gray-900">{addon.name}</h6>
// // //                           <p className="text-sm text-[#FF6500] font-semibold">
// // //                             +{addon.price.toFixed(2)} L.E
// // //                           </p>
// // //                         </div>
// // //                         <div className="flex items-center gap-2">
// // //                           <button
// // //                             onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
// // //                             className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
// // //                             disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
// // //                           >
// // //                             <Minus className="w-4 h-4" />
// // //                           </button>
// // //                           <span className="w-8 text-center font-medium">
// // //                             {selectedAddons[addon.id] || 0}
// // //                           </span>
// // //                           <button
// // //                             onClick={() => handleAddonChange(addon.id, (selectedAddons[addon.id] || 0) + 1)}
// // //                             className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors"
// // //                           >
// // //                             <Plus className="w-4 h-4" />
// // //                           </button>
// // //                         </div>
// // //                       </div>
// // //                     ))}
// // //                   </div>
// // //                 </div>
// // //               )}
// // //             </div>
// // //           </div>
// // //         </div>
// // //       </div>
// // //     );
// // //   };

// // //   // Loading state
// // //   if (loading) {
// // //     return (
// // //       <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
// // //         <div className="text-center">
// // //           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
// // //           <p className="text-gray-600">Loading menu...</p>
// // //         </div>
// // //       </div>
// // //     );
// // //   }

// // //   // Error state
// // //   if (error) {
// // //     return (
// // //       <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
// // //         <div className="text-center bg-white p-8 rounded-xl shadow-lg">
// // //           <div className="text-red-500 text-5xl mb-4">⚠️</div>
// // //           <h2 className="text-xl font-bold text-gray-900 mb-2">Error Loading Menu</h2>
// // //           <p className="text-gray-600 mb-4">{error}</p>
// // //           <button
// // //             onClick={() => window.location.reload()}
// // //             className="px-6 py-2 bg-[#FF6500] text-white rounded-lg hover:bg-[#FF6500]/90 transition-colors"
// // //           >
// // //             Try Again
// // //           </button>
// // //         </div>
// // //       </div>
// // //     );
// // //   }

// // //   return (
// // //     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
// // //       <div className="max-w-8xl mx-auto px-4 py-8">
// // //         {/* Menu Selector */}
// // //         {menus.length > 1 && (
// // //           <div className="mb-6">
// // //             <select
// // //               value={selectedMenu?.id || ''}
// // //               onChange={(e) => {
// // //                 const menu = menus.find(m => m.id === parseInt(e.target.value));
// // //                 setSelectedMenu(menu);
// // //               }}
// // //               className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// // //             >
// // //               <option value="">Select Menu</option>
// // //               {menus.map(menu => (
// // //                 <option key={menu.id} value={menu.id}>
// // //                   {menu.name} {menu.is_default ? '(Default)' : ''}
// // //                 </option>
// // //               ))}
// // //             </select>
// // //           </div>
// // //         )}

// // //         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
// // //           {/* Menu Section */}
// // //           <div className="lg:col-span-2">
// // //             {/* Search Bar */}
// // //             <div className="relative mb-6">
// // //               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
// // //               <input
// // //                 type="text"
// // //                 placeholder="Search menu items..."
// // //                 value={searchQuery}
// // //                 onChange={(e) => setSearchQuery(e.target.value)}
// // //                 className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// // //               />
// // //             </div>

// // //             {/* Categories */}
// // //             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
// // //               {transformedCategories.map((category) => {
// // //                 const IconComponent = category.icon;
// // //                 return (
// // //                   <button
// // //                     key={category.name}
// // //                     onClick={() => setActiveCategory(category.name)}
// // //                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
// // //                       activeCategory === category.name
// // //                         ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
// // //                         : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
// // //                     }`}
// // //                   >
// // //                     <IconComponent className="w-5 h-5" />
// // //                     {category.name}
// // //                   </button>
// // //                 );
// // //               })}
// // //             </div>

// // //             {/* Menu Items Grid */}
// // //             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
// // //               {filteredItems.map((item) => (
// // //                 <div
// // //                   key={item.id}
// // //                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
// // //                   onClick={() => handleProductClick(item)}
// // //                 >
// // //                   {/* Variant Badge */}
// // //                   {item.hasVariations && (
// // //                     <div className="absolute top-3 right-3 z-10">
// // //                       <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
// // //                         <div className="flex items-center gap-1">
// // //                           <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
// // //                           Variant
// // //                         </div>
// // //                       </div>
// // //                     </div>
// // //                   )}

// // //                   {/* Spicy Badge */}
// // //                   {item.isSpicy && (
// // //                     <div className="absolute top-3 left-3 z-10">
// // //                       <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
// // //                         🌶️ Spicy
// // //                       </div>
// // //                     </div>
// // //                   )}

// // //                   <div className="relative">
// // //                     <img
// // //                       src={item.image}
// // //                       alt={item.name}
// // //                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
// // //                       onError={(e) => {
// // //                         e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
// // //                       }}
// // //                     />
// // //                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
// // //                   </div>

// // //                   <div className="p-4">
// // //                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
// // //                       {item.name}
// // //                     </h3>
// // //                     <div className="flex items-center justify-between">
// // //                       <div className="flex items-center gap-2">
// // //                         <span className="text-lg font-bold text-[#FF6500]">
// // //                           {item.price === 0
// // //                             ? "depends on variant"
// // //                             : `${item.price.toFixed(2)} L.E`}
// // //                         </span>
// // //                       </div>
// // //                     </div>
// // //                     {/* Additional info */}
// // //                     <div className="mt-2 text-xs text-gray-500">
// // //                       {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
// // //                       {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
// // //                     </div>
// // //                   </div>
// // //                 </div>
// // //               ))}
// // //             </div>

// // //             {filteredItems.length === 0 && (
// // //               <div className="text-center py-12">
// // //                 <div className="text-gray-400 text-6xl mb-4">🍽️</div>
// // //                 <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
// // //                 <p className="text-gray-500">
// // //                   {searchQuery 
// // //                     ? `No items match "${searchQuery}"`
// // //                     : `No items available in "${activeCategory}" category`
// // //                   }
// // //                 </p>
// // //               </div>
// // //             )}
// // //           </div>

// // //           {/* Order Summary */}
// // //           <OrderSummary
// // //             cart={cart}
// // //             setCart={setCart}
// // //             addToCart={addToCart}
// // //             removeFromCart={removeFromCart}
// // //             deleteFromCart={deleteFromCart}
// // //           />
// // //         </div>
// // //       </div>

// // //       {/* Variant Modal */}
// // //       <VariantModal />
// // //     </div>
// // //   );
// // // };

// // // export default Pos;
// // "use client";
// // import React, { useState, useEffect } from "react";
// // import { useRouter } from 'next/navigation';
// // import {
// //   Search,
// //   RotateCcw,
// //   ShoppingCart,
// //   Plus,
// //   Minus,
// //   User,
// //   MapPin,
// //   Truck,
// //   Coffee,
// //   Cookie,
// //   Droplets,
// //   CreditCard,
// //   Printer,
// //   Users,
// //   Percent,
// //   Trash2,
// //   Grid3X3,
// //   Cake,
// //   X,
// //   MessageSquare,
// //   Edit3,
// //   Phone,
// // } from "lucide-react";
// // import OrderSummary from "./OrderSummary";
// // import { fetchMenuItems, fetchCategories, fetchMenus } from '../../../../../lib/api';

// // const Pos = ({ token = null }) => {
// //   const router = useRouter();
// //   const [activeCategory, setActiveCategory] = useState("Show All");
// //   const [orderType, setOrderType] = useState("Pickup");
// //   const [cart, setCart] = useState([]);
// //   const [searchQuery, setSearchQuery] = useState("");
// //   const [pax, setPax] = useState(1);
// //   const [discount, setDiscount] = useState(0);
// //   const [deliveryTable, setDeliveryTable] = useState("");
// //   const [showVariantModal, setShowVariantModal] = useState(false);
// //   const [selectedProduct, setSelectedProduct] = useState(null);
// //   const [selectedAddons, setSelectedAddons] = useState({});
  
// //   // API Data States
// //   const [categories, setCategories] = useState([]);
// //   const [menuItems, setMenuItems] = useState([]);
// //   const [menus, setMenus] = useState([]);
// //   const [loading, setLoading] = useState(true);
// //   const [error, setError] = useState(null);
// //   const [selectedMenu, setSelectedMenu] = useState(null);

// //   // Helper function to get icons for categories
// //   function getIconForCategory(categoryName) {
// //     const name = categoryName.toLowerCase();
// //     if (name.includes('drink') || name.includes('beverage')) return Coffee;
// //     if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
// //     if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
// //     return Cookie; // default icon
// //   }

// //   // Load data on component mount
// //   useEffect(() => {
// //     const loadData = async () => {
// //       try {
// //         setLoading(true);
// //         setError(null);

// //         const [categoriesData, menuItemsData, menusData] = await Promise.all([
// //           fetchCategories(token),
// //           fetchMenuItems(token),
// //           fetchMenus(token)
// //         ]);

// //         setCategories(categoriesData);
// //         setMenuItems(menuItemsData);
// //         setMenus(menusData);

// //         // Set default menu (first active menu or first menu)
// //         const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
// //         setSelectedMenu(defaultMenu);

// //       } catch (err) {
// //         console.error('Error loading data:', err);
// //         // Redirect to login page instead of showing error
// //         router.push('/login');
// //         return;
// //       } finally {
// //         setLoading(false);
// //       }
// //     };

// //     loadData();
// //   }, [token, router]);

// //   // Transform API categories to match your existing structure
// //   const transformedCategories = [
// //     { name: "Show All", icon: Grid3X3 },
// //     ...categories
// //       .filter(cat => cat.is_active)
// //       .map(cat => ({
// //         name: cat.name,
// //         icon: getIconForCategory(cat.name),
// //         id: cat.id,
// //         code: cat.code
// //       }))
// //   ];

// //   // Transform API menu items to match your existing structure
// //   const transformedMenuItems = menuItems
// //     .filter(item => item.is_active)
// //     .map(item => ({
// //       id: item.id,
// //       name: item.name,
// //       price: parseFloat(item.base_price),
// //       category: item.category?.name || 'Other',
// //       categoryId: item.category_id,
// //       image: item.image_urls && item.image_urls.length > 0 
// //         ? item.image_urls[0] 
// //         : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
// //       description: item.description,
// //       shortDescription: item.short_description,
// //       hasVariations: item.variants && item.variants.length > 0,
// //       variants: item.variants?.map(variant => ({
// //         id: variant.id,
// //         name: variant.name,
// //         price: parseFloat(variant.price)
// //       })) || [],
// //       addons: item.addons?.map(addon => ({
// //         id: addon.id,
// //         name: addon.name,
// //         price: parseFloat(addon.price)
// //       })) || [],
// //       allergens: item.allergens || [],
// //       dietaryInfo: item.dietary_info || [],
// //       isSpicy: item.is_spicy,
// //       prepTime: item.prep_time_minutes,
// //       calories: item.calories
// //     }));

// //   // Filter items based on selected menu, category, and search
// //   const filteredItems = transformedMenuItems.filter((item) => {
// //     // Filter by selected menu if specified
// //     if (selectedMenu && item.menu_id && item.menu_id !== selectedMenu.id) {
// //       return false;
// //     }

// //     // Filter by category
// //     const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
// //     // Filter by search query
// //     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
// //     return matchesCategory && matchesSearch;
// //   });

// //   const getTotalItems = () =>
// //     cart.reduce((total, item) => total + item.quantity, 0);
// //   const getTotalPrice = () =>
// //     cart.reduce((total, item) => total + item.price * item.quantity, 0);
// //   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
// //   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
// //   const getTax = () => getSubTotal() * 0.14;
// //   const getFinalTotal = () => getSubTotal() + getTax();

// //   const resetOrder = () => {
// //     setCart([]);
// //     setDiscount(0);
// //     setPax(1);
// //     setDeliveryTable("");
// //     setSearchQuery("");
// //   };

// //   const handleProductClick = (item) => {
// //     if (item.hasVariations && item.variants && item.variants.length > 0) {
// //       setSelectedProduct(item);
// //       setShowVariantModal(true);
// //     } else {
// //       addToCart(item);
// //     }
// //   };

// //   const addToCart = (item, variant = null, addons = []) => {
// //     // Calculate addons price
// //     const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
// //     const cartItem = variant
// //       ? {
// //           ...item,
// //           id: `${item.id}_${variant.id}`, 
// //           name: `${item.name} - ${variant.name}`,
// //           price: variant.price + addonsPrice,
// //           originalPrice: variant.price,
// //           originalId: item.id,
// //           variantId: variant.id,
// //           variantName: variant.name,
// //           image: item.image,
// //           size: variant.name,
// //           addons: addons
// //         }
// //       : { 
// //           ...item, 
// //           image: item.image,
// //           price: item.price + addonsPrice,
// //           originalPrice: item.price,
// //           addons: addons
// //         };

// //     const existingItem = cart.find(
// //       (existingCartItem) => existingCartItem.id === cartItem.id
// //     );

// //     if (existingItem) {
// //       setCart(
// //         cart.map((existingCartItem) =>
// //           existingCartItem.id === cartItem.id
// //             ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
// //             : existingCartItem
// //         )
// //       );
// //     } else {
// //       setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
// //     }

// //     if (variant) {
// //       setShowVariantModal(false);
// //       setSelectedProduct(null);
// //     }
// //   };

// //   const removeFromCart = (itemId) => {
// //     setCart((prev) =>
// //       prev.map((item) =>
// //         item.id === itemId && item.quantity > 1
// //           ? { ...item, quantity: item.quantity - 1 }
// //           : item
// //       )
// //     );
// //   };

// //   const deleteFromCart = (itemId) => {
// //     setCart((prev) => prev.filter((item) => item.id !== itemId));
// //   };

// //   const VariantModal = () => {
// //     const [selectedAddons, setSelectedAddons] = useState({});

// //     if (!showVariantModal || !selectedProduct) return null;

// //     const handleAddonChange = (addonId, quantity) => {
// //       setSelectedAddons(prev => ({
// //         ...prev,
// //         [addonId]: quantity
// //       }));
// //     };

// //     const addProductWithAddons = (product, variant) => {
// //       const selectedAddonsArray = Object.entries(selectedAddons)
// //         .filter(([_, quantity]) => quantity > 0)
// //         .map(([addonId, quantity]) => {
// //           const addon = product.addons.find(a => a.id === addonId);
// //           return {
// //             ...addon,
// //             quantity: quantity
// //           };
// //         });

// //       addToCart(product, variant, selectedAddonsArray);
// //       setSelectedAddons({});
// //     };

// //     return (
// //       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
// //         <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
// //           <div className="relative p-6 border-b border-gray-100">
// //             <button
// //               onClick={() => {
// //                 setShowVariantModal(false);
// //                 setSelectedProduct(null);
// //                 setSelectedAddons({});
// //               }}
// //               className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
// //             >
// //               <X className="w-5 h-5 text-gray-500" />
// //             </button>
// //             <h3 className="text-xl font-bold text-gray-900 mb-2">
// //               Customize Your Order
// //             </h3>
// //             <p className="text-sm text-gray-600">Choose size and add extras</p>
// //           </div>

// //           <div className="p-6 border-b border-gray-100">
// //             <div className="flex items-center gap-4">
// //               <img
// //                 src={selectedProduct.image}
// //                 alt={selectedProduct.name}
// //                 className="w-16 h-16 rounded-lg object-cover shadow-md"
// //               />
// //               <div>
// //                 <h4 className="font-semibold text-gray-900 text-lg">
// //                   {selectedProduct.name}
// //                 </h4>
// //                 <p className="text-sm text-gray-500">
// //                   {selectedProduct.shortDescription || "Available in multiple sizes"}
// //                 </p>
// //               </div>
// //             </div>

// //             <div className="overflow-y-auto flex justify-center items-start flex-wrap">
// //               {/* Variants Section */}
// //               {selectedProduct.variants && selectedProduct.variants.length > 0 && (
// //                 <div className="p-6 border-b border-gray-100 lg:w-1/3 lg:flex-1">
// //                   <h5 className="font-semibold text-gray-900 mb-3">Choose Size</h5>
// //                   <div className="space-y-3">
// //                     {selectedProduct.variants.map((variant) => (
// //                       <div
// //                         key={variant.id}
// //                         className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
// //                         onClick={() => addProductWithAddons(selectedProduct, variant)}
// //                       >
// //                         <div className="flex-1">
// //                           <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
// //                             {variant.name}
// //                           </h6>
// //                         </div>
// //                         <div className="text-right">
// //                           <p className="font-bold text-lg text-[#FF6500]">
// //                             {variant.price.toFixed(2)} L.E
// //                           </p>
// //                         </div>
// //                       </div>
// //                     ))}
// //                   </div>
// //                 </div>
// //               )}

// //               {/* Addons Section */}
// //               {selectedProduct.addons && selectedProduct.addons.length > 0 && (
// //                 <div className="p-6 lg:w-1/3 lg:flex-1">
// //                   <h5 className="font-semibold text-gray-900 mb-3">Add Extras</h5>
// //                   <div className="space-y-3">
// //                     {selectedProduct.addons.map((addon) => (
// //                       <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
// //                         <div className="flex-1">
// //                           <h6 className="font-medium text-gray-900">{addon.name}</h6>
// //                           <p className="text-sm text-[#FF6500] font-semibold">
// //                             +{addon.price.toFixed(2)} L.E
// //                           </p>
// //                         </div>
// //                         <div className="flex items-center gap-2">
// //                           <button
// //                             onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
// //                             className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
// //                             disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
// //                           >
// //                             <Minus className="w-4 h-4" />
// //                           </button>
// //                           <span className="w-8 text-center font-medium">
// //                             {selectedAddons[addon.id] || 0}
// //                           </span>
// //                           <button
// //                             onClick={() => handleAddonChange(addon.id, (selectedAddons[addon.id] || 0) + 1)}
// //                             className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors"
// //                           >
// //                             <Plus className="w-4 h-4" />
// //                           </button>
// //                         </div>
// //                       </div>
// //                     ))}
// //                   </div>
// //                 </div>
// //               )}
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     );
// //   };

// //   // Loading state
// //   if (loading) {
// //     return (
// //       <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
// //         <div className="text-center">
// //           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
// //           <p className="text-gray-600">Loading menu...</p>
// //         </div>
// //       </div>
// //     );
// //   }

// //   return (
// //     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
// //       <div className="max-w-8xl mx-auto px-4 py-8">
// //         {/* Menu Selector */}
// //         {menus.length > 1 && (
// //           <div className="mb-6">
// //             <select
// //               value={selectedMenu?.id || ''}
// //               onChange={(e) => {
// //                 const menu = menus.find(m => m.id === parseInt(e.target.value));
// //                 setSelectedMenu(menu);
// //               }}
// //               className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// //             >
// //               <option value="">Select Menu</option>
// //               {menus.map(menu => (
// //                 <option key={menu.id} value={menu.id}>
// //                   {menu.name} {menu.is_default ? '(Default)' : ''}
// //                 </option>
// //               ))}
// //             </select>
// //           </div>
// //         )}

// //         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
// //           {/* Menu Section */}
// //           <div className="lg:col-span-2">
// //             {/* Search Bar */}
// //             <div className="relative mb-6">
// //               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
// //               <input
// //                 type="text"
// //                 placeholder="Search menu items..."
// //                 value={searchQuery}
// //                 onChange={(e) => setSearchQuery(e.target.value)}
// //                 className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// //               />
// //             </div>

// //             {/* Categories */}
// //             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
// //               {transformedCategories.map((category) => {
// //                 const IconComponent = category.icon;
// //                 return (
// //                   <button
// //                     key={category.name}
// //                     onClick={() => setActiveCategory(category.name)}
// //                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
// //                       activeCategory === category.name
// //                         ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
// //                         : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
// //                     }`}
// //                   >
// //                     <IconComponent className="w-5 h-5" />
// //                     {category.name}
// //                   </button>
// //                 );
// //               })}
// //             </div>

// //             {/* Menu Items Grid */}
// //             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
// //               {filteredItems.map((item) => (
// //                 <div
// //                   key={item.id}
// //                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
// //                   onClick={() => handleProductClick(item)}
// //                 >
// //                   {/* Variant Badge */}
// //                   {item.hasVariations && (
// //                     <div className="absolute top-3 right-3 z-10">
// //                       <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
// //                         <div className="flex items-center gap-1">
// //                           <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
// //                           Variant
// //                         </div>
// //                       </div>
// //                     </div>
// //                   )}

// //                   {/* Spicy Badge */}
// //                   {item.isSpicy && (
// //                     <div className="absolute top-3 left-3 z-10">
// //                       <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
// //                         🌶️ Spicy
// //                       </div>
// //                     </div>
// //                   )}

// //                   <div className="relative">
// //                     <img
// //                       src={item.image}
// //                       alt={item.name}
// //                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
// //                       onError={(e) => {
// //                         e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
// //                       }}
// //                     />
// //                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
// //                   </div>

// //                   <div className="p-4">
// //                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
// //                       {item.name}
// //                     </h3>
// //                     <div className="flex items-center justify-between">
// //                       <div className="flex items-center gap-2">
// //                         <span className="text-lg font-bold text-[#FF6500]">
// //                           {item.price === 0
// //                             ? "depends on variant"
// //                             : `${item.price.toFixed(2)} L.E`}
// //                         </span>
// //                       </div>
// //                     </div>
// //                     {/* Additional info */}
// //                     <div className="mt-2 text-xs text-gray-500">
// //                       {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
// //                       {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
// //                     </div>
// //                   </div>
// //                 </div>
// //               ))}
// //             </div>

// //             {filteredItems.length === 0 && (
// //               <div className="text-center py-12">
// //                 <div className="text-gray-400 text-6xl mb-4">🍽️</div>
// //                 <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
// //                 <p className="text-gray-500">
// //                   {searchQuery 
// //                     ? `No items match "${searchQuery}"`
// //                     : `No items available in "${activeCategory}" category`
// //                   }
// //                 </p>
// //               </div>
// //             )}
// //           </div>

// //           {/* Order Summary */}
// //           <OrderSummary
// //             cart={cart}
// //             setCart={setCart}
// //             addToCart={addToCart}
// //             removeFromCart={removeFromCart}
// //             deleteFromCart={deleteFromCart}
// //           />
// //         </div>
// //       </div>

// //       {/* Variant Modal */}
// //       <VariantModal />
// //     </div>
// //   );
// // };

// // export default Pos;
// "use client";
// import React, { useState, useEffect } from "react";
// import { useRouter } from 'next/navigation';
// import {
//   Search,
//   RotateCcw,
//   ShoppingCart,
//   Plus,
//   Minus,
//   User,
//   MapPin,
//   Truck,
//   Coffee,
//   Cookie,
//   Droplets,
//   CreditCard,
//   Printer,
//   Users,
//   Percent,
//   Trash2,
//   Grid3X3,
//   Cake,
//   X,
//   MessageSquare,
//   Edit3,
//   Phone,
// } from "lucide-react";
// import OrderSummary from "./OrderSummary";
// import { fetchMenuItems, fetchCategories, fetchMenus } from '../../../../../lib/api';

// const Pos = ({ token = null }) => {
//   const router = useRouter();
//   const [activeCategory, setActiveCategory] = useState("Show All");
//   const [orderType, setOrderType] = useState("Pickup");
//   const [cart, setCart] = useState([]);
//   const [searchQuery, setSearchQuery] = useState("");
//   const [pax, setPax] = useState(1);
//   const [discount, setDiscount] = useState(0);
//   const [deliveryTable, setDeliveryTable] = useState("");
//   const [showVariantModal, setShowVariantModal] = useState(false);
//   const [selectedProduct, setSelectedProduct] = useState(null);
//   const [selectedAddons, setSelectedAddons] = useState({});
  
//   // API Data States
//   const [categories, setCategories] = useState([]);
//   const [menuItems, setMenuItems] = useState([]);
//   const [menus, setMenus] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const [selectedMenu, setSelectedMenu] = useState(null);

//   // Helper function to get icons for categories
//   function getIconForCategory(categoryName) {
//     const name = categoryName.toLowerCase();
//     if (name.includes('drink') || name.includes('beverage')) return Coffee;
//     if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
//     if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
//     return Cookie; // default icon
//   }

//   // Load data on component mount
//   useEffect(() => {
//     const loadData = async () => {
//       try {
//         setLoading(true);
//         setError(null);

//         const [categoriesData, menuItemsData, menusData] = await Promise.all([
//           fetchCategories(token),
//           fetchMenuItems(token),
//           fetchMenus(token)
//         ]);

//         setCategories(categoriesData);
//         setMenuItems(menuItemsData);
//         setMenus(menusData);

//         // Set default menu (first active menu or first menu)
//         const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
//         setSelectedMenu(defaultMenu);

//       } catch (err) {
//         console.error('Error loading data:', err);
//         // Redirect to login page instead of showing error
//         router.push('/login');
//         return;
//       } finally {
//         setLoading(false);
//       }
//     };

//     loadData();
//   }, [token, router]);

//   // Transform API categories to match your existing structure
//   const transformedCategories = [
//     { name: "Show All", icon: Grid3X3, id: "show-all", uniqueKey: "show-all" },
//     ...categories
//       .filter(cat => cat.is_active)
//       .map(cat => ({
//         name: cat.name,
//         icon: getIconForCategory(cat.name),
//         id: cat.id,
//         code: cat.code,
//         uniqueKey: `category-${cat.id}` // Use unique key based on ID
//       }))
//   ];

//   // Transform API menu items to match your existing structure
//   const transformedMenuItems = menuItems
//     .filter(item => item.is_active)
//     .map(item => ({
//       id: item.id,
//       name: item.name,
//       price: parseFloat(item.base_price),
//       category: item.category?.name || 'Other',
//       categoryId: item.category_id,
//       image: item.image_urls && item.image_urls.length > 0 
//         ? item.image_urls[0] 
//         : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
//       description: item.description,
//       shortDescription: item.short_description,
//       hasVariations: item.variants && item.variants.length > 0,
//       variants: item.variants?.map(variant => ({
//         id: variant.id,
//         name: variant.name,
//         price: parseFloat(variant.price)
//       })) || [],
//       addons: item.addons?.map(addon => ({
//         id: addon.id,
//         name: addon.name,
//         price: parseFloat(addon.price)
//       })) || [],
//       allergens: item.allergens || [],
//       dietaryInfo: item.dietary_info || [],
//       isSpicy: item.is_spicy,
//       prepTime: item.prep_time_minutes,
//       calories: item.calories
//     }));

//   // Filter items based on selected menu, category, and search
//   const filteredItems = transformedMenuItems.filter((item) => {
//     // Filter by selected menu if specified
//     if (selectedMenu && item.menu_id && item.menu_id !== selectedMenu.id) {
//       return false;
//     }

//     // Filter by category
//     const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
//     // Filter by search query
//     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
//     return matchesCategory && matchesSearch;
//   });

//   const getTotalItems = () =>
//     cart.reduce((total, item) => total + item.quantity, 0);
//   const getTotalPrice = () =>
//     cart.reduce((total, item) => total + item.price * item.quantity, 0);
//   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
//   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
//   const getTax = () => getSubTotal() * 0.14;
//   const getFinalTotal = () => getSubTotal() + getTax();

//   const resetOrder = () => {
//     setCart([]);
//     setDiscount(0);
//     setPax(1);
//     setDeliveryTable("");
//     setSearchQuery("");
//   };

//   const handleProductClick = (item) => {
//     if (item.hasVariations && item.variants && item.variants.length > 0) {
//       setSelectedProduct(item);
//       setShowVariantModal(true);
//     } else {
//       addToCart(item);
//     }
//   };

//   const addToCart = (item, variant = null, addons = []) => {
//     // Calculate addons price
//     const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
//     const cartItem = variant
//       ? {
//           ...item,
//           id: `${item.id}_${variant.id}`, 
//           name: `${item.name} - ${variant.name}`,
//           price: variant.price + addonsPrice,
//           originalPrice: variant.price,
//           originalId: item.id,
//           variantId: variant.id,
//           variantName: variant.name,
//           image: item.image,
//           size: variant.name,
//           addons: addons
//         }
//       : { 
//           ...item, 
//           image: item.image,
//           price: item.price + addonsPrice,
//           originalPrice: item.price,
//           addons: addons
//         };

//     const existingItem = cart.find(
//       (existingCartItem) => existingCartItem.id === cartItem.id
//     );

//     if (existingItem) {
//       setCart(
//         cart.map((existingCartItem) =>
//           existingCartItem.id === cartItem.id
//             ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
//             : existingCartItem
//         )
//       );
//     } else {
//       setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
//     }

//     if (variant) {
//       setShowVariantModal(false);
//       setSelectedProduct(null);
//     }
//   };

//   const removeFromCart = (itemId) => {
//     setCart((prev) =>
//       prev.map((item) =>
//         item.id === itemId && item.quantity > 1
//           ? { ...item, quantity: item.quantity - 1 }
//           : item
//       )
//     );
//   };

//   const deleteFromCart = (itemId) => {
//     setCart((prev) => prev.filter((item) => item.id !== itemId));
//   };

//   const VariantModal = () => {
//     const [selectedAddons, setSelectedAddons] = useState({});

//     if (!showVariantModal || !selectedProduct) return null;

//     const handleAddonChange = (addonId, quantity) => {
//       setSelectedAddons(prev => ({
//         ...prev,
//         [addonId]: quantity
//       }));
//     };

//     const addProductWithAddons = (product, variant) => {
//       const selectedAddonsArray = Object.entries(selectedAddons)
//         .filter(([_, quantity]) => quantity > 0)
//         .map(([addonId, quantity]) => {
//           const addon = product.addons.find(a => a.id === addonId);
//           return {
//             ...addon,
//             quantity: quantity
//           };
//         });

//       addToCart(product, variant, selectedAddonsArray);
//       setSelectedAddons({});
//     };

//     return (
//       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
//         <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
//           <div className="relative p-6 border-b border-gray-100">
//             <button
//               onClick={() => {
//                 setShowVariantModal(false);
//                 setSelectedProduct(null);
//                 setSelectedAddons({});
//               }}
//               className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
//             >
//               <X className="w-5 h-5 text-gray-500" />
//             </button>
//             <h3 className="text-xl font-bold text-gray-900 mb-2">
//               Customize Your Order
//             </h3>
//             <p className="text-sm text-gray-600">Choose size and add extras</p>
//           </div>

//           <div className="p-6 border-b border-gray-100">
//             <div className="flex items-center gap-4">
//               <img
//                 src={selectedProduct.image}
//                 alt={selectedProduct.name}
//                 className="w-16 h-16 rounded-lg object-cover shadow-md"
//               />
//               <div>
//                 <h4 className="font-semibold text-gray-900 text-lg">
//                   {selectedProduct.name}
//                 </h4>
//                 <p className="text-sm text-gray-500">
//                   {selectedProduct.shortDescription || "Available in multiple sizes"}
//                 </p>
//               </div>
//             </div>

//             <div className="overflow-y-auto flex justify-center items-start flex-wrap">
//               {/* Variants Section */}
//               {selectedProduct.variants && selectedProduct.variants.length > 0 && (
//                 <div className="p-6 border-b border-gray-100 lg:w-1/3 lg:flex-1">
//                   <h5 className="font-semibold text-gray-900 mb-3">Choose Size</h5>
//                   <div className="space-y-3">
//                     {selectedProduct.variants.map((variant) => (
//                       <div
//                         key={variant.id}
//                         className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
//                         onClick={() => addProductWithAddons(selectedProduct, variant)}
//                       >
//                         <div className="flex-1">
//                           <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
//                             {variant.name}
//                           </h6>
//                         </div>
//                         <div className="text-right">
//                           <p className="font-bold text-lg text-[#FF6500]">
//                             {variant.price.toFixed(2)} L.E
//                           </p>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               )}

//               {/* Addons Section */}
//               {selectedProduct.addons && selectedProduct.addons.length > 0 && (
//                 <div className="p-6 lg:w-1/3 lg:flex-1">
//                   <h5 className="font-semibold text-gray-900 mb-3">Add Extras</h5>
//                   <div className="space-y-3">
//                     {selectedProduct.addons.map((addon) => (
//                       <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
//                         <div className="flex-1">
//                           <h6 className="font-medium text-gray-900">{addon.name}</h6>
//                           <p className="text-sm text-[#FF6500] font-semibold">
//                             +{addon.price.toFixed(2)} L.E
//                           </p>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <button
//                             onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
//                             className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
//                             disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
//                           >
//                             <Minus className="w-4 h-4" />
//                           </button>
//                           <span className="w-8 text-center font-medium">
//                             {selectedAddons[addon.id] || 0}
//                           </span>
//                           <button
//                             onClick={() => handleAddonChange(addon.id, (selectedAddons[addon.id] || 0) + 1)}
//                             className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors"
//                           >
//                             <Plus className="w-4 h-4" />
//                           </button>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               )}
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   };

//   // Loading state
//   if (loading) {
//     return (
//       <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
//           <p className="text-gray-600">Loading menu...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
//       <div className="max-w-8xl mx-auto px-4 py-8">
//         {/* Menu Selector */}
//         {menus.length > 1 && (
//           <div className="mb-6">
//             <select
//               value={selectedMenu?.id || ''}
//               onChange={(e) => {
//                 const menu = menus.find(m => m.id === parseInt(e.target.value));
//                 setSelectedMenu(menu);
//               }}
//               className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//             >
//               <option value="">Select Menu</option>
//               {menus.map(menu => (
//                 <option key={menu.id} value={menu.id}>
//                   {menu.name} {menu.is_default ? '(Default)' : ''}
//                 </option>
//               ))}
//             </select>
//           </div>
//         )}

//         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
//           {/* Menu Section */}
//           <div className="lg:col-span-2">
//             {/* Search Bar */}
//             <div className="relative mb-6">
//               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <input
//                 type="text"
//                 placeholder="Search menu items..."
//                 value={searchQuery}
//                 onChange={(e) => setSearchQuery(e.target.value)}
//                 className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//               />
//             </div>

//             {/* Categories */}
//             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
//               {transformedCategories.map((category) => {
//                 const IconComponent = category.icon;
//                 return (
//                   <button
//                     key={category.uniqueKey} // Use uniqueKey instead of name
//                     onClick={() => setActiveCategory(category.name)}
//                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
//                       activeCategory === category.name
//                         ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
//                         : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
//                     }`}
//                   >
//                     <IconComponent className="w-5 h-5" />
//                     {category.name}
//                   </button>
//                 );
//               })}
//             </div>

//             {/* Menu Items Grid */}
//             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
//               {filteredItems.map((item) => (
//                 <div
//                   key={item.id}
//                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
//                   onClick={() => handleProductClick(item)}
//                 >
//                   {/* Variant Badge */}
//                   {item.hasVariations && (
//                     <div className="absolute top-3 right-3 z-10">
//                       <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
//                         <div className="flex items-center gap-1">
//                           <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
//                           Variant
//                         </div>
//                       </div>
//                     </div>
//                   )}

//                   {/* Spicy Badge */}
//                   {item.isSpicy && (
//                     <div className="absolute top-3 left-3 z-10">
//                       <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
//                         🌶️ Spicy
//                       </div>
//                     </div>
//                   )}

//                   <div className="relative">
//                     <img
//                       src={item.image}
//                       alt={item.name}
//                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
//                       onError={(e) => {
//                         e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
//                       }}
//                     />
//                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
//                   </div>

//                   <div className="p-4">
//                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
//                       {item.name}
//                     </h3>
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <span className="text-lg font-bold text-[#FF6500]">
//                           {item.price === 0
//                             ? "depends on variant"
//                             : `${item.price.toFixed(2)} L.E`}
//                         </span>
//                       </div>
//                     </div>
//                     {/* Additional info */}
//                     <div className="mt-2 text-xs text-gray-500">
//                       {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
//                       {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
//                     </div>
//                   </div>
//                 </div>
//               ))}
//             </div>

//             {filteredItems.length === 0 && (
//               <div className="text-center py-12">
//                 <div className="text-gray-400 text-6xl mb-4">🍽️</div>
//                 <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
//                 <p className="text-gray-500">
//                   {searchQuery 
//                     ? `No items match "${searchQuery}"`
//                     : `No items available in "${activeCategory}" category`
//                   }
//                 </p>
//               </div>
//             )}
//           </div>

//           {/* Order Summary */}
//           <OrderSummary
//             cart={cart}
//             setCart={setCart}
//             addToCart={addToCart}
//             removeFromCart={removeFromCart}
//             deleteFromCart={deleteFromCart}
//           />
//         </div>
//       </div>

//       {/* Variant Modal */}
//       <VariantModal />
//     </div>
//   );
// };

// export default Pos;
// "use client";
// import React, { useState, useEffect } from "react";
// import { useRouter } from 'next/navigation';
// import {
//   Search,
//   RotateCcw,
//   ShoppingCart,
//   Plus,
//   Minus,
//   User,
//   MapPin,
//   Truck,
//   Coffee,
//   Cookie,
//   Droplets,
//   CreditCard,
//   Printer,
//   Users,
//   Percent,
//   Trash2,
//   Grid3X3,
//   Cake,
//   X,
//   MessageSquare,
//   Edit3,
//   Phone,
// } from "lucide-react";
// import OrderSummary from "./OrderSummary";
// import { fetchMenuItems, fetchCategories, fetchMenus } from '../../../../../lib/api';

// const Pos = ({ token = null }) => {
//   const router = useRouter();
//   const [activeCategory, setActiveCategory] = useState("Show All");
//   const [orderType, setOrderType] = useState("Pickup");
//   const [cart, setCart] = useState([]);
//   const [searchQuery, setSearchQuery] = useState("");
//   const [pax, setPax] = useState(1);
//   const [discount, setDiscount] = useState(0);
//   const [deliveryTable, setDeliveryTable] = useState("");
//   const [showVariantModal, setShowVariantModal] = useState(false);
//   const [selectedProduct, setSelectedProduct] = useState(null);
//   const [selectedAddons, setSelectedAddons] = useState({});
  
//   // API Data States
//   const [categories, setCategories] = useState([]);
//   const [menuItems, setMenuItems] = useState([]);
//   const [menus, setMenus] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const [selectedMenu, setSelectedMenu] = useState(null);

//   // Helper function to get icons for categories
//   function getIconForCategory(categoryName) {
//     const name = categoryName.toLowerCase();
//     if (name.includes('drink') || name.includes('beverage')) return Coffee;
//     if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
//     if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
//     return Cookie; // default icon
//   }

//   // Load data on component mount
//   useEffect(() => {
//     const loadData = async () => {
//       try {
//         setLoading(true);
//         setError(null);

//         const [categoriesData, menuItemsData, menusData] = await Promise.all([
//           fetchCategories(token),
//           fetchMenuItems(token),
//           fetchMenus(token)
//         ]);

//         setCategories(categoriesData);
//         setMenuItems(menuItemsData);
//         setMenus(menusData);

//         // Set default menu (first active menu or first menu)
//         const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
//         setSelectedMenu(defaultMenu);

//       } catch (err) {
//         console.error('Error loading data:', err);
//         // Redirect to login page instead of showing error
//         router.push('/login');
//         return;
//       } finally {
//         setLoading(false);
//       }
//     };

//     loadData();
//   }, [token, router]);

//   // Transform API categories to match your existing structure and remove duplicates
//   const uniqueCategories = categories
//     .filter(cat => cat.is_active)
//     .reduce((unique, cat) => {
//       // Check if category name already exists
//       const existing = unique.find(item => item.name === cat.name);
//       if (!existing) {
//         unique.push({
//           name: cat.name,
//           icon: getIconForCategory(cat.name),
//           id: cat.id,
//           code: cat.code
//         });
//       }
//       return unique;
//     }, []);

//   const transformedCategories = [
//     { name: "Show All", icon: Grid3X3, id: "show-all" },
//     ...uniqueCategories
//   ];

//   // Transform API menu items to match your existing structure
//   const transformedMenuItems = menuItems
//     .filter(item => item.is_active)
//     .map(item => ({
//       id: item.id,
//       name: item.name,
//       price: parseFloat(item.base_price),
//       category: item.category?.name || 'Other',
//       categoryId: item.category_id,
//       image: item.image_urls && item.image_urls.length > 0 
//         ? item.image_urls[0] 
//         : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
//       description: item.description,
//       shortDescription: item.short_description,
//       hasVariations: item.variants && item.variants.length > 0,
//       variants: item.variants?.map(variant => ({
//         id: variant.id,
//         name: variant.name,
//         price: parseFloat(variant.price)
//       })) || [],
//       addons: item.addons?.map(addon => ({
//         id: addon.id,
//         name: addon.name,
//         price: parseFloat(addon.price)
//       })) || [],
//       allergens: item.allergens || [],
//       dietaryInfo: item.dietary_info || [],
//       isSpicy: item.is_spicy,
//       prepTime: item.prep_time_minutes,
//       calories: item.calories
//     }));

//   // Filter items based on selected menu, category, and search
//   const filteredItems = transformedMenuItems.filter((item) => {
//     // Filter by selected menu if specified
//     if (selectedMenu && item.menu_id && item.menu_id !== selectedMenu.id) {
//       return false;
//     }

//     // Filter by category
//     const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
//     // Filter by search query
//     const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
//     return matchesCategory && matchesSearch;
//   });

//   const getTotalItems = () =>
//     cart.reduce((total, item) => total + item.quantity, 0);
//   const getTotalPrice = () =>
//     cart.reduce((total, item) => total + item.price * item.quantity, 0);
//   const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
//   const getSubTotal = () => getTotalPrice() - getDiscountAmount();
//   const getTax = () => getSubTotal() * 0.14;
//   const getFinalTotal = () => getSubTotal() + getTax();

//   const resetOrder = () => {
//     setCart([]);
//     setDiscount(0);
//     setPax(1);
//     setDeliveryTable("");
//     setSearchQuery("");
//   };

//   const handleProductClick = (item) => {
//     if (item.hasVariations && item.variants && item.variants.length > 0) {
//       setSelectedProduct(item);
//       setShowVariantModal(true);
//     } else {
//       addToCart(item);
//     }
//   };

//   const addToCart = (item, variant = null, addons = []) => {
//     // Calculate addons price
//     const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
//     const cartItem = variant
//       ? {
//           ...item,
//           id: `${item.id}_${variant.id}`, 
//           name: `${item.name} - ${variant.name}`,
//           price: variant.price + addonsPrice,
//           originalPrice: variant.price,
//           originalId: item.id,
//           variantId: variant.id,
//           variantName: variant.name,
//           image: item.image,
//           size: variant.name,
//           addons: addons
//         }
//       : { 
//           ...item, 
//           image: item.image,
//           price: item.price + addonsPrice,
//           originalPrice: item.price,
//           addons: addons
//         };

//     const existingItem = cart.find(
//       (existingCartItem) => existingCartItem.id === cartItem.id
//     );

//     if (existingItem) {
//       setCart(
//         cart.map((existingCartItem) =>
//           existingCartItem.id === cartItem.id
//             ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
//             : existingCartItem
//         )
//       );
//     } else {
//       setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
//     }

//     if (variant) {
//       setShowVariantModal(false);
//       setSelectedProduct(null);
//     }
//   };

//   const removeFromCart = (itemId) => {
//     setCart((prev) =>
//       prev.map((item) =>
//         item.id === itemId && item.quantity > 1
//           ? { ...item, quantity: item.quantity - 1 }
//           : item
//       )
//     );
//   };

//   const deleteFromCart = (itemId) => {
//     setCart((prev) => prev.filter((item) => item.id !== itemId));
//   };

//   const VariantModal = () => {
//     const [selectedAddons, setSelectedAddons] = useState({});

//     if (!showVariantModal || !selectedProduct) return null;

//     const handleAddonChange = (addonId, quantity) => {
//       setSelectedAddons(prev => ({
//         ...prev,
//         [addonId]: quantity
//       }));
//     };

//     const addProductWithAddons = (product, variant) => {
//       const selectedAddonsArray = Object.entries(selectedAddons)
//         .filter(([_, quantity]) => quantity > 0)
//         .map(([addonId, quantity]) => {
//           const addon = product.addons.find(a => a.id === addonId);
//           return {
//             ...addon,
//             quantity: quantity
//           };
//         });

//       addToCart(product, variant, selectedAddonsArray);
//       setSelectedAddons({});
//     };

//     return (
//       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
//         <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
//           <div className="relative p-6 border-b border-gray-100">
//             <button
//               onClick={() => {
//                 setShowVariantModal(false);
//                 setSelectedProduct(null);
//                 setSelectedAddons({});
//               }}
//               className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
//             >
//               <X className="w-5 h-5 text-gray-500" />
//             </button>
//             <h3 className="text-xl font-bold text-gray-900 mb-2">
//               Customize Your Order
//             </h3>
//             <p className="text-sm text-gray-600">Choose size and add extras</p>
//           </div>

//           <div className="p-6 border-b border-gray-100">
//             <div className="flex items-center gap-4">
//               <img
//                 src={selectedProduct.image}
//                 alt={selectedProduct.name}
//                 className="w-16 h-16 rounded-lg object-cover shadow-md"
//               />
//               <div>
//                 <h4 className="font-semibold text-gray-900 text-lg">
//                   {selectedProduct.name}
//                 </h4>
//                 <p className="text-sm text-gray-500">
//                   {selectedProduct.shortDescription || "Available in multiple sizes"}
//                 </p>
//               </div>
//             </div>

//             <div className="overflow-y-auto flex justify-center items-start flex-wrap">
//               {/* Variants Section */}
//               {selectedProduct.variants && selectedProduct.variants.length > 0 && (
//                 <div className="p-6 border-b border-gray-100 lg:w-1/3 lg:flex-1">
//                   <h5 className="font-semibold text-gray-900 mb-3">Choose Size</h5>
//                   <div className="space-y-3">
//                     {selectedProduct.variants.map((variant) => (
//                       <div
//                         key={variant.id}
//                         className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
//                         onClick={() => addProductWithAddons(selectedProduct, variant)}
//                       >
//                         <div className="flex-1">
//                           <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
//                             {variant.name}
//                           </h6>
//                         </div>
//                         <div className="text-right">
//                           <p className="font-bold text-lg text-[#FF6500]">
//                             {variant.price.toFixed(2)} L.E
//                           </p>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               )}

//               {/* Addons Section */}
//               {selectedProduct.addons && selectedProduct.addons.length > 0 && (
//                 <div className="p-6 lg:w-1/3 lg:flex-1">
//                   <h5 className="font-semibold text-gray-900 mb-3">Add Extras</h5>
//                   <div className="space-y-3">
//                     {selectedProduct.addons.map((addon) => (
//                       <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
//                         <div className="flex-1">
//                           <h6 className="font-medium text-gray-900">{addon.name}</h6>
//                           <p className="text-sm text-[#FF6500] font-semibold">
//                             +{addon.price.toFixed(2)} L.E
//                           </p>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <button
//                             onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
//                             className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
//                             disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
//                           >
//                             <Minus className="w-4 h-4" />
//                           </button>
//                           <span className="w-8 text-center font-medium">
//                             {selectedAddons[addon.id] || 0}
//                           </span>
//                           <button
//                             onClick={() => handleAddonChange(addon.id, (selectedAddons[addon.id] || 0) + 1)}
//                             className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors"
//                           >
//                             <Plus className="w-4 h-4" />
//                           </button>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               )}
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   };

//   // Loading state
//   if (loading) {
//     return (
//       <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
//           <p className="text-gray-600">Loading menu...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
//       <div className="max-w-8xl mx-auto px-4 py-8">
//         {/* Menu Selector */}
//         {menus.length > 1 && (
//           <div className="mb-6">
//             <select
//               value={selectedMenu?.id || ''}
//               onChange={(e) => {
//                 const menu = menus.find(m => m.id === parseInt(e.target.value));
//                 setSelectedMenu(menu);
//               }}
//               className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//             >
//               <option value="">Select Menu</option>
//               {menus.map(menu => (
//                 <option key={menu.id} value={menu.id}>
//                   {menu.name} {menu.is_default ? '(Default)' : ''}
//                 </option>
//               ))}
//             </select>
//           </div>
//         )}

//         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
//           {/* Menu Section */}
//           <div className="lg:col-span-2">
//             {/* Search Bar */}
//             <div className="relative mb-6">
//               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <input
//                 type="text"
//                 placeholder="Search menu items..."
//                 value={searchQuery}
//                 onChange={(e) => setSearchQuery(e.target.value)}
//                 className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//               />
//             </div>

//             {/* Categories */}
//             <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
//               {transformedCategories.map((category) => {
//                 const IconComponent = category.icon;
//                 return (
//                   <button
//                     key={category.name} // Use name as key since we removed duplicates
//                     onClick={() => setActiveCategory(category.name)}
//                     className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
//                       activeCategory === category.name
//                         ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
//                         : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
//                     }`}
//                   >
//                     <IconComponent className="w-5 h-5" />
//                     {category.name}
//                   </button>
//                 );
//               })}
//             </div>

//             {/* Menu Items Grid */}
//             <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
//               {filteredItems.map((item) => (
//                 <div
//                   key={item.id}
//                   className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
//                   onClick={() => handleProductClick(item)}
//                 >
//                   {/* Variant Badge */}
//                   {item.hasVariations && (
//                     <div className="absolute top-3 right-3 z-10">
//                       <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
//                         <div className="flex items-center gap-1">
//                           <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
//                           Variant
//                         </div>
//                       </div>
//                     </div>
//                   )}

//                   {/* Spicy Badge */}
//                   {item.isSpicy && (
//                     <div className="absolute top-3 left-3 z-10">
//                       <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
//                         🌶️ Spicy
//                       </div>
//                     </div>
//                   )}

//                   <div className="relative">
//                     <img
//                       src={item.image}
//                       alt={item.name}
//                       className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
//                       onError={(e) => {
//                         e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
//                       }}
//                     />
//                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
//                   </div>

//                   <div className="p-4">
//                     <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
//                       {item.name}
//                     </h3>
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <span className="text-lg font-bold text-[#FF6500]">
//                           {item.price === 0
//                             ? "depends on variant"
//                             : `${item.price.toFixed(2)} L.E`}
//                         </span>
//                       </div>
//                     </div>
//                     {/* Additional info */}
//                     <div className="mt-2 text-xs text-gray-500">
//                       {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
//                       {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
//                     </div>
//                   </div>
//                 </div>
//               ))}
//             </div>

//             {filteredItems.length === 0 && (
//               <div className="text-center py-12">
//                 <div className="text-gray-400 text-6xl mb-4">🍽️</div>
//                 <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
//                 <p className="text-gray-500">
//                   {searchQuery 
//                     ? `No items match "${searchQuery}"`
//                     : `No items available in "${activeCategory}" category`
//                   }
//                 </p>
//               </div>
//             )}
//           </div>

//           {/* Order Summary */}
//           <OrderSummary
//             cart={cart}
//             setCart={setCart}
//             addToCart={addToCart}
//             removeFromCart={removeFromCart}
//             deleteFromCart={deleteFromCart}
//           />
//         </div>
//       </div>

//       {/* Variant Modal */}
//       <VariantModal />
//     </div>
//   );
// };

// export default Pos;
"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import {
  Search,
  RotateCcw,
  ShoppingCart,
  Plus,
  Minus,
  User,
  MapPin,
  Truck,
  Coffee,
  Cookie,
  Droplets,
  CreditCard,
  Printer,
  Users,
  Percent,
  Trash2,
  Grid3X3,
  Cake,
  X,
  MessageSquare,
  Edit3,
  Phone,
} from "lucide-react";
import OrderSummary from "./OrderSummary";
import { fetchMenuItems, fetchCategories, fetchMenus, fetchMenuItemVariants, fetchMenuItemAddons } from '../../../../../lib/api';

const Pos = ({ token = null }) => {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("Show All");
  const [orderType, setOrderType] = useState("Pickup");
  const [cart, setCart] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [pax, setPax] = useState(1);
  const [discount, setDiscount] = useState(0);
  const [deliveryTable, setDeliveryTable] = useState("");
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedAddons, setSelectedAddons] = useState({});
  const [variantsLoading, setVariantsLoading] = useState(false);
  const [addonsLoading, setAddonsLoading] = useState(false);
  
  // API Data States
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [menus, setMenus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMenu, setSelectedMenu] = useState(null);

  // Helper function to get icons for categories
  function getIconForCategory(categoryName) {
    const name = categoryName.toLowerCase();
    if (name.includes('drink') || name.includes('beverage')) return Coffee;
    if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
    if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
    return Cookie; // default icon
  }

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [categoriesData, menuItemsData, menusData] = await Promise.all([
          fetchCategories(token),
          fetchMenuItems(token),
          fetchMenus(token)
        ]);

        setCategories(categoriesData);
        setMenuItems(menuItemsData);
        setMenus(menusData);

        // Set default menu (first active menu or first menu)
        const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
        setSelectedMenu(defaultMenu);

      } catch (err) {
        console.error('Error loading data:', err);
        // Redirect to login page instead of showing error
        router.push('/login');
        return;
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [token, router]);

  // Transform API categories to match your existing structure and remove duplicates
  const uniqueCategories = categories
    .filter(cat => cat.is_active)
    .reduce((unique, cat) => {
      // Check if category name already exists
      const existing = unique.find(item => item.name === cat.name);
      if (!existing) {
        unique.push({
          name: cat.name,
          icon: getIconForCategory(cat.name),
          id: cat.id,
          code: cat.code
        });
      }
      return unique;
    }, []);

  const transformedCategories = [
    { name: "Show All", icon: Grid3X3, id: "show-all" },
    ...uniqueCategories
  ];

  // Transform API menu items to match your existing structure
  const transformedMenuItems = menuItems
    .filter(item => item.is_active)
    .map(item => ({
      id: item.id,
      name: item.name,
      price: parseFloat(item.base_price),
      category: item.category?.name || 'Other',
      categoryId: item.category_id,
      image: item.image_urls && item.image_urls.length > 0 
        ? item.image_urls[0] 
        : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
      description: item.description,
      shortDescription: item.short_description,
      // Check if item has variants by looking at the variants array length
      hasVariations: item.variants && item.variants.length > 0,
      allergens: item.allergens || [],
      dietaryInfo: item.dietary_info || [],
      isSpicy: item.is_spicy,
      prepTime: item.prep_time_minutes,
      calories: item.calories,
      menuId: item.menu_id
    }));

  // Filter items based on selected menu, category, and search
  const filteredItems = transformedMenuItems.filter((item) => {
    // Filter by selected menu if specified
    if (selectedMenu && item.menuId && item.menuId !== selectedMenu.id) {
      return false;
    }

    // Filter by category
    const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
    // Filter by search query
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const getTotalItems = () =>
    cart.reduce((total, item) => total + item.quantity, 0);
  const getTotalPrice = () =>
    cart.reduce((total, item) => total + item.price * item.quantity, 0);
  const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
  const getSubTotal = () => getTotalPrice() - getDiscountAmount();
  const getTax = () => getSubTotal() * 0.14;
  const getFinalTotal = () => getSubTotal() + getTax();

  const resetOrder = () => {
    setCart([]);
    setDiscount(0);
    setPax(1);
    setDeliveryTable("");
    setSearchQuery("");
  };

  // Load variants and addons for selected product
  const loadProductDetails = async (item) => {
    setSelectedProduct({ ...item, variants: [], addons: [] });
    setShowVariantModal(true);
    setVariantsLoading(true);
    setAddonsLoading(true);

    try {
      // Fetch variants and addons simultaneously
      const [variantsData, addonsData] = await Promise.all([
        fetchMenuItemVariants(item.id, token),
        fetchMenuItemAddons(item.id, token)
      ]);

      // Transform variants data
      const transformedVariants = variantsData.map(variant => ({
        id: variant.id,
        name: variant.name,
        code: variant.code,
        price: parseFloat(item.price) + parseFloat(variant.price_modifier),
        priceModifier: parseFloat(variant.price_modifier),
        isDefault: variant.is_default,
        sortOrder: variant.sort_order
      }));

      // Transform addons data
      const transformedAddons = addonsData.map(addon => ({
        id: addon.id,
        name: addon.name,
        code: addon.code,
        price: parseFloat(addon.price),
        cost: parseFloat(addon.cost || 0),
        isRequired: addon.is_required,
        maxQuantity: addon.max_quantity,
        groupName: addon.addon_group_name,
        sortOrder: addon.sort_order
      }));

      // Update selected product with fetched data
      setSelectedProduct(prev => ({
        ...prev,
        variants: transformedVariants,
        addons: transformedAddons,
        hasVariations: transformedVariants.length > 0
      }));

    } catch (error) {
      console.error('Error loading product details:', error);
      // Keep modal open but show empty variants/addons
      setSelectedProduct(prev => ({
        ...prev,
        variants: [],
        addons: [],
        hasVariations: false
      }));
    } finally {
      setVariantsLoading(false);
      setAddonsLoading(false);
    }
  };

  const handleProductClick = (item) => {
    if (item.hasVariations) {
      // Load variants and addons dynamically
      loadProductDetails(item);
    } else {
      // For products without variations, still check for addons
      loadProductDetails(item);
    }
  };

  const addToCart = (item, variant = null, addons = []) => {
    // Calculate addons price
    const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
    const cartItem = variant
      ? {
          ...item,
          id: `${item.id}_${variant.id}`, 
          name: `${item.name} - ${variant.name}`,
          price: variant.price + addonsPrice,
          originalPrice: variant.price,
          originalId: item.id,
          variantId: variant.id,
          variantName: variant.name,
          image: item.image,
          size: variant.name,
          addons: addons
        }
      : { 
          ...item, 
          image: item.image,
          price: item.price + addonsPrice,
          originalPrice: item.price,
          addons: addons
        };

    const existingItem = cart.find(
      (existingCartItem) => existingCartItem.id === cartItem.id
    );

    if (existingItem) {
      setCart(
        cart.map((existingCartItem) =>
          existingCartItem.id === cartItem.id
            ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
            : existingCartItem
        )
      );
    } else {
      setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
    }

    if (variant || addons.length > 0) {
      setShowVariantModal(false);
      setSelectedProduct(null);
      setSelectedAddons({});
    }
  };

  const removeFromCart = (itemId) => {
    setCart((prev) =>
      prev.map((item) =>
        item.id === itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      )
    );
  };

  const deleteFromCart = (itemId) => {
    setCart((prev) => prev.filter((item) => item.id !== itemId));
  };

  const VariantModal = () => {
    const [selectedAddons, setSelectedAddons] = useState({});

    if (!showVariantModal || !selectedProduct) return null;

    const handleAddonChange = (addonId, quantity) => {
      setSelectedAddons(prev => ({
        ...prev,
        [addonId]: quantity
      }));
    };

    const addProductWithAddons = (product, variant = null) => {
      const selectedAddonsArray = Object.entries(selectedAddons)
        .filter(([_, quantity]) => quantity > 0)
        .map(([addonId, quantity]) => {
          const addon = product.addons.find(a => a.id.toString() === addonId.toString());
          return {
            ...addon,
            quantity: quantity
          };
        });

      addToCart(product, variant, selectedAddonsArray);
      setSelectedAddons({});
    };

    const hasVariants = selectedProduct.variants && selectedProduct.variants.length > 0;
    const hasAddons = selectedProduct.addons && selectedProduct.addons.length > 0;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
        <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
          <div className="relative p-6 border-b border-gray-100">
            <button
              onClick={() => {
                setShowVariantModal(false);
                setSelectedProduct(null);
                setSelectedAddons({});
              }}
              className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Customize Your Order
            </h3>
            <p className="text-sm text-gray-600">
              {hasVariants ? 'Choose size and add extras' : 'Add extras'}
            </p>
          </div>

          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center gap-4">
              <img
                src={selectedProduct.image}
                alt={selectedProduct.name}
                className="w-16 h-16 rounded-lg object-cover shadow-md"
              />
              <div>
                <h4 className="font-semibold text-gray-900 text-lg">
                  {selectedProduct.name}
                </h4>
                <p className="text-sm text-gray-500">
                  {selectedProduct.shortDescription || selectedProduct.description}
                </p>
                <p className="text-lg font-bold text-[#FF6500] mt-1">
                  {selectedProduct.price.toFixed(2)} L.E
                </p>
              </div>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            <div className="flex flex-col lg:flex-row">
              {/* Variants Section */}
              {hasVariants && (
                <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 lg:w-1/2">
                  <h5 className="font-semibold text-gray-900 mb-4">Choose Size</h5>
                  {variantsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6500] mx-auto mb-2"></div>
                      <p className="text-sm text-gray-500">Loading sizes...</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedProduct.variants.map((variant) => (
                        <div
                          key={variant.id}
                          className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
                          onClick={() => addProductWithAddons(selectedProduct, variant)}
                        >
                          <div className="flex-1">
                            <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
                              {variant.name}
                            </h6>
                            {variant.priceModifier !== 0 && (
                              <p className="text-sm text-gray-500">
                                {variant.priceModifier > 0 ? '+' : ''}{variant.priceModifier.toFixed(2)} L.E
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-lg text-[#FF6500]">
                              {variant.price.toFixed(2)} L.E
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Addons Section */}
              {hasAddons && (
                <div className="p-6 lg:w-1/2">
                  <h5 className="font-semibold text-gray-900 mb-4">Add Extras</h5>
                  {addonsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6500] mx-auto mb-2"></div>
                      <p className="text-sm text-gray-500">Loading extras...</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedProduct.addons.map((addon) => (
                        <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <h6 className="font-medium text-gray-900">{addon.name}</h6>
                            <p className="text-sm text-[#FF6500] font-semibold">
                              +{addon.price.toFixed(2)} L.E
                            </p>
                            {addon.isRequired && (
                              <span className="text-xs text-red-500 font-medium">Required</span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
                              className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors disabled:opacity-50"
                              disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
                            >
                              <Minus className="w-4 h-4" />
                            </button>
                            <span className="w-8 text-center font-medium">
                              {selectedAddons[addon.id] || 0}
                            </span>
                            <button
                              onClick={() => {
                                const currentQuantity = selectedAddons[addon.id] || 0;
                                if (addon.maxQuantity && currentQuantity >= addon.maxQuantity) return;
                                handleAddonChange(addon.id, currentQuantity + 1);
                              }}
                              className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors disabled:opacity-50"
                              disabled={addon.maxQuantity && (selectedAddons[addon.id] || 0) >= addon.maxQuantity}
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Add to Cart Button for products without variants or when only addons */}
          {(!hasVariants || (!variantsLoading && selectedProduct.variants.length === 0)) && (
            <div className="p-6 border-t border-gray-100">
              <button
                onClick={() => addProductWithAddons(selectedProduct)}
                className="w-full bg-[#FF6500] text-white py-3 px-6 rounded-xl font-semibold hover:bg-[#FF6500]/90 transition-colors"
                disabled={addonsLoading}
              >
                {addonsLoading ? 'Loading...' : 'Add to Cart'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading menu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-8xl mx-auto px-4 py-8">
        {/* Menu Selector */}
        {menus.length > 1 && (
          <div className="mb-6">
            <select
              value={selectedMenu?.id || ''}
              onChange={(e) => {
                const menu = menus.find(m => m.id === parseInt(e.target.value));
                setSelectedMenu(menu);
              }}
              className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
            >
              <option value="">Select Menu</option>
              {menus.map(menu => (
                <option key={menu.id} value={menu.id}>
                  {menu.name} {menu.is_default ? '(Default)' : ''}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Menu Section */}
          <div className="lg:col-span-2">
            {/* Search Bar */}
            <div className="relative mb-6">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            {/* Categories */}
            <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
              {transformedCategories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.name}
                    onClick={() => setActiveCategory(category.name)}
                    className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
                      activeCategory === category.name
                        ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
                        : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    {category.name}
                  </button>
                );
              })}
            </div>

            {/* Menu Items Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
              {filteredItems.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
                  onClick={() => handleProductClick(item)}
                >
                  {/* Variant Badge */}
                  {item.hasVariations && (
                    <div className="absolute top-3 right-3 z-10">
                      <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                          Variant
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Spicy Badge */}
                  {item.isSpicy && (
                    <div className="absolute top-3 left-3 z-10">
                      <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                        🌶️ Spicy
                      </div>
                    </div>
                  )}

                  <div className="relative">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
                      {item.name}
                    </h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-[#FF6500]">
                          {item.price === 0
                            ? "varies"
                            : `${item.price.toFixed(2)} L.E`}
                        </span>
                      </div>
                    </div>
                    {/* Additional info */}
                    <div className="mt-2 text-xs text-gray-500">
                      {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
                      {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredItems.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">🍽️</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
                <p className="text-gray-500">
                  {searchQuery 
                    ? `No items match "${searchQuery}"`
                    : `No items available in "${activeCategory}" category`
                  }
                </p>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <OrderSummary
            cart={cart}
            setCart={setCart}
            addToCart={addToCart}
            removeFromCart={removeFromCart}
            deleteFromCart={deleteFromCart}
          />
        </div>
      </div>

      {/* Variant Modal */}
      <VariantModal />
    </div>
  );
};

export default Pos;