"use client"
import React, { useState } from 'react';
import { Calendar, ChevronDown, RefreshCw, Plus } from 'lucide-react';

const Orders = () => {
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState('10 Seconds');
  const [dateFrom, setDateFrom] = useState('07/06/2025');
  const [dateTo, setDateTo] = useState('07/06/2025');

  const orders = [
    {
      id: '#114',
      status: 'Order Preparing',
      time: 'July 06, 2025 22:50 PM',
      quantity: '1 KOT',
      price: '34.20',
      currency: 'L.E',
      customerName: 'MAHMOUD MOHAMED',
      isNew: true,
      kotBadge: true
    },
    {
      id: '#113',
      status: 'Order Preparing',
      time: 'July 06, 2025 22:32 PM',
      quantity: '1 KOT',
      price: '125.40',
      currency: 'L.<PERSON>',
      customerName: 'MAHMOUD MOHAMED',
      isNew: true,
      kotBadge: true
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Orders (2)</h1>
            <div className="flex items-center gap-4">
              {/* Auto Refresh Toggle */}
              <div className="flex items-center gap-3">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-12 h-6 rounded-full transition-colors ${
                      autoRefresh ? 'bg-[#FF6500]' : 'bg-gray-300'
                    }`}>
                      <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                        autoRefresh ? 'translate-x-6' : 'translate-x-0.5'
                      } mt-0.5`}></div>
                    </div>
                  </div>
                  Auto Refresh
                </label>
                <select 
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                >
                  <option>10 Seconds</option>
                  <option>30 Seconds</option>
                  <option>1 Minute</option>
                  <option>5 Minutes</option>
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
                  <option>All</option>
                  <option>Today</option>
                  <option>This Week</option>
                </select>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Today</span>
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            <span className="text-sm text-gray-500">To</span>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            <select className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
              <option>Show All Orders</option>
              <option>Pending Orders</option>
              <option>Completed Orders</option>
              <option>Cancelled Orders</option>
            </select>

            <select className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
              <option>Show All Waiters</option>
              <option>Waiter 1</option>
              <option>Waiter 2</option>
            </select>

            <button className="ml-auto bg-[#FF6500] hover:bg-[#E55A00] text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Order
            </button>
          </div>
        </div>

        {/* Orders Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {orders.map((order) => (
            <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              {/* Order Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-lg font-bold text-gray-600">--</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Order {order.id}</h3>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">{order.status}</span>
                      </div>
                    </div>
                  </div>
                  {order.kotBadge && (
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md text-xs font-medium">
                      KOT
                    </span>
                  )}
                </div>
                
                <p className="text-sm text-gray-500 mb-4">{order.time}</p>
                
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {order.price} <span className="text-sm font-normal text-gray-500">{order.currency}</span>
                </div>
                
                <div className="text-sm text-gray-600 mb-4">{order.quantity}</div>
                
                {order.isNew && (
                  <div className="flex items-center gap-2 mb-4">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs font-medium">
                      New KOT
                    </span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-xs text-gray-600">👤</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{order.customerName}</span>
                </div>
              </div>

              {/* Order Actions */}
              <div className="p-4 bg-gray-50 rounded-b-xl">
                <div className="flex gap-2">
                  <button className="flex-1 bg-[#FF6500] hover:bg-[#E55A00] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    View Details
                  </button>
                  <button className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State (when no orders) */}
        {orders.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <RefreshCw className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your filters or date range</p>
            <button className="bg-[#FF6500] hover:bg-[#E55A00] text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto">
              <Plus className="w-4 h-4" />
              Create New Order
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;